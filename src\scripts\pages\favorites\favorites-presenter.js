import StoryIdb from "../../data/story-idb";

class FavoritesPresenter {
  constructor({ view }) {
    this._view = view;
  }

  async init() {
    await this.loadFavorites();
  }

  async loadFavorites() {
    try {
      this._view.showLoading();

      // Get favorite stories from IndexedDB
      const stories = await StoryIdb.getAllStories();

      this._view.hideLoading();

      if (stories && stories.length > 0) {
        this._view.showFavorites(stories);
      } else {
        this._view.showEmptyFavorites();
      }
    } catch (error) {
      console.error("Error loading favorite stories:", error);
      this._view.hideLoading();
      this._view.showError(error.message || "Failed to load favorite stories");
    }
  }

  async clearFavorites() {
    try {
      this._view.showLoading();

      // Clear all stories from IndexedDB
      await StoryIdb.clearStories();

      this._view.hideLoading();
      this._view.showEmptyFavorites();
    } catch (error) {
      console.error("Error clearing favorite stories:", error);
      this._view.hideLoading();
      this._view.showError(error.message || "Failed to clear favorite stories");
    }
  }
}

export default FavoritesPresenter;
