import StoryAPI from "../../data/api";
import StoryIdb from "../../data/story-idb";
import { parseActivePathname } from "../../routes/url-parser";
import AuthService from "../../utils/auth";

class DetailPresenter {
  constructor({ view, authService, storyAPI }) {
    this._view = view;
    this._authService = authService || AuthService;
    this._storyAPI = storyAPI || StoryAPI;
    this._story = null;
  }

  async init() {
    // Get story ID from URL
    const { id } = parseActivePathname();

    if (!id) {
      this._view.showError("Story ID not found in URL.");
      return;
    }

    await this.loadStoryDetail(id);
  }

  async loadStoryDetail(id) {
    try {
      this._view.showLoading();

      // Get auth data
      const auth = this._authService.getAuth();

      if (!auth || !auth.token) {
        throw new Error("Authentication token not found");
      }

      // For testing purposes, check if we already have the story in IndexedDB
      try {
        const cachedStory = await StoryIdb.getStory(id);
        if (cachedStory) {
          console.log("Using cached story from IndexedDB:", cachedStory);
          this._story = cachedStory;
          this._view.showStoryDetail(this._story);

          // If story has location, show map
          if (this._story.lat && this._story.lon) {
            this._view.showMap(this._story);
          }

          this._view.hideLoading();
          return;
        }
      } catch (idbError) {
        console.error("Error accessing IndexedDB:", idbError);
        // Continue with API fetch if IndexedDB fails
      }

      // If using test token, don't try to fetch from API
      if (auth.token === "test-token") {
        console.log("Using test token, but story not found in IndexedDB");
        this._view.hideLoading();
        this._view.showError(
          "Story not found. Please go back to the home page."
        );
        return;
      }

      try {
        // Try to fetch story detail from API
        const response = await this._storyAPI.getStoryDetail(id, auth.token);

        if (response.error === false) {
          // Store story
          this._story = response.story;

          // Save story to IndexedDB for offline use
          try {
            await StoryIdb.saveStory(this._story);
          } catch (saveError) {
            console.error("Error saving to IndexedDB:", saveError);
            // Continue even if saving to IndexedDB fails
          }

          // Show story detail
          this._view.showStoryDetail(this._story);

          // If story has location, show map
          if (this._story.lat && this._story.lon) {
            this._view.showMap(this._story);
          }
        } else {
          throw new Error(response.message || "Failed to load story");
        }
      } catch (error) {
        console.error("Error loading story detail from API:", error);

        // If API request fails, try to load from IndexedDB
        console.log("Trying to load story from IndexedDB...");
        try {
          const story = await StoryIdb.getStory(id);

          if (story) {
            console.log("Successfully loaded story from IndexedDB");
            this._story = story;

            // Show story detail
            this._view.showStoryDetail(this._story);

            // If story has location, show map
            if (this._story.lat && this._story.lon) {
              this._view.showMap(this._story);
            }
          } else {
            // If story not in IndexedDB, show error
            this._view.showError(
              "Story not found. Please go back and try again."
            );
          }
        } catch (idbError) {
          console.error("Error accessing IndexedDB for fallback:", idbError);
          this._view.showError(
            "Failed to load story. Please go back to the home page."
          );
        }
      }

      this._view.hideLoading();
    } catch (error) {
      console.error("Error in loadStoryDetail:", error);
      this._view.hideLoading();
      this._view.showError(error.message);
    }
  }

  getStory() {
    return this._story;
  }
}

export default DetailPresenter;
