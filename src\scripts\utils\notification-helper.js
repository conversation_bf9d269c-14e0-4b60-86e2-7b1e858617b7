import { Workbox } from "workbox-window";
import CONFIG from "../config";

const NotificationHelper = {
  // VAPID public key from the API documentation
  vapidPublicKey: CONFIG.VAPID_PUBLIC_KEY,

  // Check if service worker is supported
  isServiceWorkerSupported() {
    return "serviceWorker" in navigator;
  },

  // Check if push notification is supported
  isPushNotificationSupported() {
    return this.isServiceWorkerSupported() && "PushManager" in window;
  },

  // Register service worker
  async registerServiceWorker() {
    if (!this.isServiceWorkerSupported()) {
      console.log("Service Worker is not supported in this browser");
      return null;
    }

    try {
      // Check if a service worker is already controlling the page
      if (navigator.serviceWorker.controller) {
        console.log("Service worker already controlling the page");
        return navigator.serviceWorker.ready;
      }

      // Try to register service worker directly
      const registration = await navigator.serviceWorker.register("/sw.js");
      console.log("Service worker registered directly:", registration);
      return registration;
    } catch (directError) {
      console.warn("Direct service worker registration failed, trying Workbox:", directError);

      // Fall back to Workbox
      try {
        const wb = new Workbox("/sw.js");
        const registration = await wb.register();
        console.log("Service worker registered via Workbox:", registration);
        return registration;
      } catch (workboxError) {
        console.error("Workbox service worker registration failed:", workboxError);
        // Return a mock registration to prevent app from breaking
        return {
          pushManager: {
            getSubscription: () => Promise.resolve(null),
            subscribe: () => Promise.reject(new Error("Service worker registration failed")),
          },
        };
      }
    }
  },

  // Request notification permission
  async requestPermission() {
    if (!("Notification" in window)) {
      console.log("Notification is not supported in this browser");
      return false;
    }

    try {
      const result = await Notification.requestPermission();
      if (result === "granted") {
        console.log("Notification permission granted");
        return true;
      }
      console.log("Notification permission denied");
      return false;
    } catch (error) {
      console.error("Error requesting notification permission:", error);
      return false;
    }
  },

  // Convert base64 string to Uint8Array
  urlBase64ToUint8Array(base64String) {
    const padding = "=".repeat((4 - (base64String.length % 4)) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, "+")
      .replace(/_/g, "/");

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  },

  // Subscribe to push notification
  async subscribeToPushNotification(serviceWorkerRegistration) {
    try {
      // Get the subscription
      let subscription = await serviceWorkerRegistration.pushManager.getSubscription();

      // If already subscribed, convert to JSON and return
      if (subscription) {
        console.log("Using existing subscription");
        const subscriptionJson = subscription.toJSON();
        if (!subscriptionJson.keys || !subscriptionJson.keys.p256dh || !subscriptionJson.keys.auth) {
          throw new Error("Existing subscription is missing required keys");
        }
        return subscriptionJson;
      }

      // Subscribe to push notification
      console.log("Subscribing with VAPID key:", this.vapidPublicKey);

      // Convert VAPID key to Uint8Array
      const applicationServerKey = this.urlBase64ToUint8Array(this.vapidPublicKey);
      console.log("Application server key length:", applicationServerKey.length);

      // Try to subscribe
      subscription = await serviceWorkerRegistration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: applicationServerKey,
      });

      console.log("Successfully subscribed to push notification");

      // Convert subscription to JSON
      const subscriptionJson = subscription.toJSON();
      console.log("Subscription JSON:", subscriptionJson);

      // Verify that keys exist
      if (!subscriptionJson.keys || !subscriptionJson.keys.p256dh || !subscriptionJson.keys.auth) {
        throw new Error("Subscription keys (p256dh or auth) are missing");
      }

      return subscriptionJson;
    } catch (error) {
      console.error("Failed to subscribe to push notification:", error);
      if (Notification.permission === "denied") {
        console.error("Notification permission denied");
      }
      return null;
    }
  },

  // Unsubscribe from push notification
  async unsubscribeFromPushNotification(serviceWorkerRegistration) {
    try {
      const subscription = await serviceWorkerRegistration.pushManager.getSubscription();
      if (!subscription) {
        console.log("No subscription to unsubscribe from");
        return true;
      }

      // Convert subscription to JSON for consistency
      const subscriptionJson = subscription.toJSON();
      console.log("Unsubscribing from subscription:", subscriptionJson);

      // Verify that keys exist
      if (!subscriptionJson.keys || !subscriptionJson.keys.p256dh || !subscriptionJson.keys.auth) {
        throw new Error("Subscription keys (p256dh or auth) are missing");
      }

      // Unsubscribe locally
      const result = await subscription.unsubscribe();
      console.log("Successfully unsubscribed from push notification");
      return result;
    } catch (error) {
      console.error("Failed to unsubscribe from push notification:", error);
      return true; // Simulate success to avoid breaking the app
    }
  },

  // Check if already subscribed to push notification
  async isSubscribedToPushNotification(serviceWorkerRegistration) {
    if (!serviceWorkerRegistration) {
      return false;
    }

    try {
      const subscription = await serviceWorkerRegistration.pushManager.getSubscription();
      if (!subscription) {
        return false;
      }

      const subscriptionJson = subscription.toJSON();
      return !!(subscriptionJson.keys && subscriptionJson.keys.p256dh && subscriptionJson.keys.auth);
    } catch (error) {
      console.error("Error checking push notification subscription:", error);
      return false;
    }
  },
};

export default NotificationHelper;