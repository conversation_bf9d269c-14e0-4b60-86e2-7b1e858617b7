import CONFIG from "../config";

const API_ENDPOINT = {
  REGISTER: `${CONFIG.BASE_URL}/register`,
  LOGIN: `${CONFIG.BASE_URL}/login`,
  GET_STORIES: `${CONFIG.BASE_URL}/stories`,
  ADD_STORY: `${CONFIG.BASE_URL}/stories`,
  ADD_GUEST_STORY: `${CONFIG.BASE_URL}/stories/guest`,
  DETAIL_STORY: (id) => `${CONFIG.BASE_URL}/stories/${id}`,
  SUBSCRIBE_NOTIFICATION: `${CONFIG.BASE_URL}/notifications/subscribe`,
  UNSUBSCRIBE_NOTIFICATION: `${CONFIG.BASE_URL}/notifications/unsubscribe`,
};

class StoryAPI {
  static async register({ name, email, password }) {
    const response = await fetch(API_ENDPOINT.REGISTER, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        name,
        email,
        password,
      }),
    });

    const responseJson = await response.json();
    return responseJson;
  }

  static async login({ email, password }) {
    const response = await fetch(API_ENDPOINT.LOGIN, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email,
        password,
      }),
    });

    const responseJson = await response.json();
    return responseJson;
  }

  static async getStories(token, options = {}) {
    // Build query parameters
    const params = new URLSearchParams();

    if (options.page !== undefined) {
      params.append("page", options.page);
    }

    if (options.size !== undefined) {
      params.append("size", options.size);
    }

    if (options.location !== undefined) {
      params.append("location", options.location);
    }

    // Construct URL with query parameters
    const url = `${API_ENDPOINT.GET_STORIES}${
      params.toString() ? `?${params.toString()}` : ""
    }`;

    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const responseJson = await response.json();
    return responseJson;
  }

  static async addStory({ description, photo, lat, lon }, token) {
    // Create FormData object
    const formData = new FormData();

    // Append data to FormData
    formData.append("description", description);
    formData.append("photo", photo);

    // Only append location if provided
    if (lat && lon) {
      formData.append("lat", lat);
      formData.append("lon", lon);
    }

    const response = await fetch(API_ENDPOINT.ADD_STORY, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    const responseJson = await response.json();
    return responseJson;
  }

  static async addGuestStory({ description, photo, lat, lon }) {
    // Create FormData object
    const formData = new FormData();

    // Append data to FormData
    formData.append("description", description);
    formData.append("photo", photo);

    // Only append location if provided
    if (lat && lon) {
      formData.append("lat", lat);
      formData.append("lon", lon);
    }

    const response = await fetch(API_ENDPOINT.ADD_GUEST_STORY, {
      method: "POST",
      body: formData,
    });

    const responseJson = await response.json();
    return responseJson;
  }

  static async getStoryDetail(id, token) {
    const response = await fetch(API_ENDPOINT.DETAIL_STORY(id), {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const responseJson = await response.json();
    return responseJson;
  }

  static async subscribeNotification(subscription, token) {
    const response = await fetch(API_ENDPOINT.SUBSCRIBE_NOTIFICATION, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        endpoint: subscription.endpoint,
        keys: {
          p256dh: subscription.keys.p256dh,
          auth: subscription.keys.auth,
        },
      }),
    });

    const responseJson = await response.json();
    return responseJson;
  }

  static async unsubscribeNotification(subscription, token) {
    const response = await fetch(API_ENDPOINT.UNSUBSCRIBE_NOTIFICATION, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        endpoint: subscription.endpoint,
      }),
    });

    const responseJson = await response.json();
    return responseJson;
  }
}

export default StoryAPI;
