/* eslint-disable no-restricted-globals */
// Import Workbox libraries
importScripts(
  "https://storage.googleapis.com/workbox-cdn/releases/7.0.0/workbox-sw.js"
);

// Initialize workbox
workbox.setConfig({
  debug: false,
});

workbox.core.setCacheNameDetails({
  prefix: "story-app",
  suffix: "v1",
  precache: "app-shell",
});

// Precache and route setup
workbox.precaching.precacheAndRoute(self.__WB_MANIFEST || []);

// Precache placeholder images for offline use
self.addEventListener("install", (event) => {
  event.waitUntil(
    caches.open("story-app-placeholders").then((cache) => {
      return cache.addAll([
        "/icons/icon-512x512.png",
        "/icons/icon-384x384.png",
        "/icons/icon-192x192.png",
        "/icons/icon-128x128.png",
        // Add local placeholder image
        "/images/placeholder.svg",
      ]);
    })
  );
});

// Cache all images with network-first strategy, then fallback to cache
workbox.routing.registerRoute(
  ({ request }) => request.destination === "image",
  new workbox.strategies.NetworkFirst({
    cacheName: "story-app-images",
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 300, // Increased for more images
        maxAgeSeconds: 365 * 24 * 60 * 60, // 1 year
      }),
      {
        cacheDidUpdate: async ({
          cacheName,
          request,
          oldResponse,
          newResponse,
        }) => {
          // Log when an image is updated in the cache
          console.log(`Image updated in cache: ${request.url}`);
        },
      },
    ],
    networkTimeoutSeconds: 3, // Timeout after 3 seconds and use cache
  })
);

// Special handling for picsum.photos images
workbox.routing.registerRoute(
  ({ url }) => url.href.includes("picsum.photos"),
  new workbox.strategies.CacheFirst({
    cacheName: "story-app-picsum-images",
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 100,
        maxAgeSeconds: 365 * 24 * 60 * 60, // 1 year
      }),
    ],
  })
);

// Special handling for favorite images
workbox.routing.registerRoute(
  ({ url, request }) =>
    request.destination === "image" &&
    (url.pathname.includes("/favorites") ||
      url.href.includes("story-api.dicoding.dev")),
  new workbox.strategies.CacheFirst({
    cacheName: "story-app-favorites-images",
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 200,
        maxAgeSeconds: 365 * 24 * 60 * 60, // 1 year
      }),
    ],
  })
);

// Special handling for detail page images
workbox.routing.registerRoute(
  ({ url, request }) =>
    request.destination === "image" && url.pathname.includes("/stories/"),
  new workbox.strategies.CacheFirst({
    cacheName: "story-app-detail-images",
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 100,
        maxAgeSeconds: 365 * 24 * 60 * 60, // 1 year
      }),
    ],
  })
);

// Explicitly cache specific images we know we'll need
self.addEventListener("install", (event) => {
  const imageUrls = [
    "https://picsum.photos/id/237/500/300",
    "https://picsum.photos/id/1/500/300",
    "https://picsum.photos/id/20/500/300",
    "https://picsum.photos/id/30/500/300",
  ];

  event.waitUntil(
    caches.open("story-app-picsum-images").then((cache) => {
      return Promise.all(
        imageUrls.map((url) => {
          return fetch(url)
            .then((response) => {
              if (response.ok) {
                return cache.put(url, response);
              }
              console.error(
                `Failed to cache ${url}: ${response.status} ${response.statusText}`
              );
              return Promise.resolve();
            })
            .catch((error) => {
              console.error(`Failed to fetch ${url} for caching: ${error}`);
              return Promise.resolve();
            });
        })
      );
    })
  );
});

// Cache API responses
workbox.routing.registerRoute(
  ({ url }) => url.origin === "https://story-api.dicoding.dev",
  new workbox.strategies.NetworkFirst({
    cacheName: "story-app-api",
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 100,
        maxAgeSeconds: 30 * 60, // 30 minutes
      }),
    ],
  })
);

// Cache page navigations (HTML)
workbox.routing.registerRoute(
  ({ request }) => request.mode === "navigate",
  new workbox.strategies.NetworkFirst({
    cacheName: "story-app-pages",
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 50,
        maxAgeSeconds: 24 * 60 * 60, // 1 day
      }),
    ],
  })
);

// Cache Google Fonts
workbox.routing.registerRoute(
  ({ url }) =>
    url.origin === "https://fonts.googleapis.com" ||
    url.origin === "https://fonts.gstatic.com",
  new workbox.strategies.StaleWhileRevalidate({
    cacheName: "story-app-google-fonts",
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 30,
        maxAgeSeconds: 60 * 60 * 24 * 365, // 1 year
      }),
    ],
  })
);

// Cache CSS and JS
workbox.routing.registerRoute(
  ({ request }) =>
    request.destination === "style" || request.destination === "script",
  new workbox.strategies.StaleWhileRevalidate({
    cacheName: "story-app-static-resources",
  })
);

// Push notification event listener
self.addEventListener("push", (event) => {
  console.log("Push event received");

  let notificationData = {
    title: "New Notification",
    options: {
      body: "This is a default notification",
      icon: "/favicon.png",
      badge: "/favicon.png",
    },
  };

  // Try to parse the data from the push event
  if (event.data) {
    try {
      const data = event.data.json();
      notificationData = {
        title: data.title || notificationData.title,
        options: {
          ...notificationData.options,
          ...data.options,
        },
      };
    } catch (error) {
      console.error("Error parsing push data:", error);
    }
  }

  // Show the notification
  const showNotificationPromise = self.registration.showNotification(
    notificationData.title,
    notificationData.options
  );

  // Wait until the notification is shown
  event.waitUntil(showNotificationPromise);
});

// Notification click event
self.addEventListener("notificationclick", (event) => {
  console.log("Notification clicked");

  // Close the notification
  event.notification.close();

  // Open the app when notification is clicked
  const urlToOpen = new URL("/", self.location.origin).href;

  // Open the URL in the same window/tab if already open, otherwise open in a new window/tab
  const promiseChain = clients
    .matchAll({
      type: "window",
      includeUncontrolled: true,
    })
    .then((windowClients) => {
      // Check if there is already a window/tab open with the target URL
      let matchingClient = null;
      for (const client of windowClients) {
        const url = new URL(client.url);
        if (url.origin === self.location.origin) {
          matchingClient = client;
          break;
        }
      }

      // If a window is already open, focus it
      if (matchingClient) {
        return matchingClient.focus();
      }

      // Otherwise, open a new window/tab
      return clients.openWindow(urlToOpen);
    });

  event.waitUntil(promiseChain);
});

// Fallback for offline pages
workbox.routing.setCatchHandler(async ({ event }) => {
  // Return the precached offline page if a document is being requested
  if (event.request.destination === "document") {
    return workbox.precaching.matchPrecache("/index.html");
  }

  // Return a fallback image if an image is being requested
  if (event.request.destination === "image") {
    console.log("Handling offline image request:", event.request.url);

    // Try to find the image in picsum cache first
    try {
      const picsumCache = await caches.open("story-app-picsum-images");

      // If the requested image is from picsum, try to find it directly
      if (event.request.url.includes("picsum.photos")) {
        const cachedResponse = await picsumCache.match(event.request);
        if (cachedResponse) {
          console.log("Found picsum image in cache:", event.request.url);
          return cachedResponse;
        }

        // If not found, try to find any picsum image
        const cachedPicsumImages = await picsumCache.keys();
        if (cachedPicsumImages.length > 0) {
          const randomIndex = Math.floor(
            Math.random() * cachedPicsumImages.length
          );
          const fallbackResponse = await picsumCache.match(
            cachedPicsumImages[randomIndex]
          );
          if (fallbackResponse) {
            console.log(
              "Using random picsum image as fallback:",
              cachedPicsumImages[randomIndex].url
            );
            return fallbackResponse;
          }
        }
      } else {
        // For non-picsum images, try to use a picsum image as fallback
        const cachedPicsumImages = await picsumCache.keys();
        if (cachedPicsumImages.length > 0) {
          const randomIndex = Math.floor(
            Math.random() * cachedPicsumImages.length
          );
          const fallbackResponse = await picsumCache.match(
            cachedPicsumImages[randomIndex]
          );
          if (fallbackResponse) {
            console.log(
              "Using picsum image as fallback for non-picsum request:",
              cachedPicsumImages[randomIndex].url
            );
            return fallbackResponse;
          }
        }
      }
    } catch (error) {
      console.error("Error accessing picsum cache:", error);
    }

    // Check what type of page request this is
    const isFavoritesRequest = event.request.url.includes("/favorites");
    const isDetailRequest = event.request.url.includes("/stories/");

    // Try appropriate cache first based on the page type
    if (isFavoritesRequest || isDetailRequest) {
      try {
        // Determine which cache to check first
        const cacheName = isFavoritesRequest
          ? "story-app-favorites-images"
          : "story-app-detail-images";

        const specificCache = await caches.open(cacheName);
        const cachedImages = await specificCache.keys();

        if (cachedImages.length > 0) {
          const randomIndex = Math.floor(Math.random() * cachedImages.length);
          const fallbackResponse = await specificCache.match(
            cachedImages[randomIndex]
          );
          if (fallbackResponse) {
            console.log(
              `Using random ${
                isFavoritesRequest ? "favorites" : "detail"
              } image as fallback:`,
              cachedImages[randomIndex].url
            );
            return fallbackResponse;
          }
        }
      } catch (error) {
        console.error(
          `Error accessing ${
            isFavoritesRequest ? "favorites" : "detail"
          } cache:`,
          error
        );
      }
    }

    // Try to find the image in any cache
    try {
      // Check these caches in order of preference
      const cacheOrder = [
        "story-app-detail-images",
        "story-app-favorites-images",
        "story-app-picsum-images",
        "story-app-images",
      ];

      for (const cacheName of cacheOrder) {
        try {
          const cache = await caches.open(cacheName);

          // First try to match the exact request
          const cachedResponse = await cache.match(event.request);
          if (cachedResponse) {
            console.log(
              `Found exact image match in ${cacheName}:`,
              event.request.url
            );
            return cachedResponse;
          }

          // If not found directly, try to find any image in this cache
          const cachedImages = await cache.keys();
          if (cachedImages.length > 0) {
            const randomIndex = Math.floor(Math.random() * cachedImages.length);
            const fallbackResponse = await cache.match(
              cachedImages[randomIndex]
            );
            if (fallbackResponse) {
              console.log(
                `Using random image from ${cacheName} as fallback:`,
                cachedImages[randomIndex].url
              );
              return fallbackResponse;
            }
          }
        } catch (error) {
          console.error(`Error checking cache ${cacheName}:`, error);
        }
      }

      // If still not found, check all other caches that might contain images
      const cacheNames = await caches.keys();
      for (const cacheName of cacheNames) {
        if (cacheName.includes("image") && !cacheOrder.includes(cacheName)) {
          try {
            const cache = await caches.open(cacheName);
            const cachedResponse = await cache.match(event.request);
            if (cachedResponse) {
              console.log(`Found image in ${cacheName}:`, event.request.url);
              return cachedResponse;
            }

            // If not found directly, try to find any image in this cache
            const cachedImages = await cache.keys();
            if (cachedImages.length > 0) {
              const randomIndex = Math.floor(
                Math.random() * cachedImages.length
              );
              const fallbackResponse = await cache.match(
                cachedImages[randomIndex]
              );
              if (fallbackResponse) {
                console.log(
                  `Using random image from ${cacheName} as fallback:`,
                  cachedImages[randomIndex].url
                );
                return fallbackResponse;
              }
            }
          } catch (error) {
            console.error(`Error checking cache ${cacheName}:`, error);
          }
        }
      }
    } catch (error) {
      console.error("Error finding image in caches:", error);
    }

    // Try to use our local placeholder SVG
    try {
      const placeholderResponse = await caches.match("/images/placeholder.svg");
      if (placeholderResponse) {
        console.log("Using local SVG placeholder for offline image");
        return placeholderResponse;
      }
    } catch (error) {
      console.log("Could not fetch local placeholder");
    }

    // As last resort, use the app icon
    try {
      const iconResponse = await workbox.precaching.matchPrecache(
        "/icons/icon-512x512.png"
      );
      if (iconResponse) {
        console.log("Using app icon as last resort fallback");
        return iconResponse;
      }
    } catch (error) {
      console.error("Error using app icon as fallback:", error);
    }

    // If all else fails, create a simple image response
    return new Response(
      '<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="200" height="200" fill="#cccccc"/><text x="50%" y="50%" font-family="Arial" font-size="24" text-anchor="middle" fill="#666666">Image Not Available</text></svg>',
      {
        headers: { "Content-Type": "image/svg+xml" },
      }
    );
  }

  // If we don't have a fallback, return an error response
  return Response.error();
});
