import AddPage from "../pages/add/add";
import DetailPage from "../pages/detail/detail.js";
import FavoritesPage from "../pages/favorites/favorites.js";
import HomePage from "../pages/home/<USER>";
import LoginPage from "../pages/login/login.js";
import LogoutPage from "../pages/logout/logout.js";
import MapsPage from "../pages/maps/maps.js";
import RegisterPage from "../pages/register/register.js";

const routes = {
  "/": new HomePage(), // Default route
  "/home": new HomePage(),
  "/add": new AddPage(),
  "/maps": new MapsPage(),
  "/favorites": new FavoritesPage(),
  "/login": new LoginPage(),
  "/register": new RegisterPage(),
  "/logout": new LogoutPage(),
  "/stories/:id": new DetailPage(),
};

export default routes;
