class ToastNotification {
  constructor() {
    this._createToastContainer();
  }

  _createToastContainer() {
    // Check if container already exists
    if (document.getElementById('toast-container')) {
      return;
    }

    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container';
    document.body.appendChild(container);
  }

  show(message, type = 'info', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    
    // Add icon based on type
    let icon = '';
    switch (type) {
      case 'success':
        icon = '✅';
        break;
      case 'error':
        icon = '❌';
        break;
      case 'warning':
        icon = '⚠️';
        break;
      case 'info':
      default:
        icon = 'ℹ️';
        break;
    }

    toast.innerHTML = `
      <div class="toast-content">
        <span class="toast-icon">${icon}</span>
        <span class="toast-message">${message}</span>
        <button class="toast-close" aria-label="Close notification">&times;</button>
      </div>
    `;

    // Add to container
    const container = document.getElementById('toast-container');
    container.appendChild(toast);

    // Add show animation
    setTimeout(() => {
      toast.classList.add('toast-show');
    }, 10);

    // Auto remove after duration
    const autoRemove = setTimeout(() => {
      this._removeToast(toast);
    }, duration);

    // Manual close button
    const closeButton = toast.querySelector('.toast-close');
    closeButton.addEventListener('click', () => {
      clearTimeout(autoRemove);
      this._removeToast(toast);
    });

    return toast;
  }

  _removeToast(toast) {
    toast.classList.add('toast-hide');
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 300);
  }

  success(message, duration = 3000) {
    return this.show(message, 'success', duration);
  }

  error(message, duration = 4000) {
    return this.show(message, 'error', duration);
  }

  warning(message, duration = 3500) {
    return this.show(message, 'warning', duration);
  }

  info(message, duration = 3000) {
    return this.show(message, 'info', duration);
  }
}

// Create global instance
const toastNotification = new ToastNotification();

export default toastNotification;
