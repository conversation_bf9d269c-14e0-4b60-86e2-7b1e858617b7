import StoryAPI from "../../data/api";
import StoryIdb from "../../data/story-idb";
import AuthService from "../../utils/auth";

class HomePresenter {
  constructor({ view, authService, storyAPI }) {
    this._view = view;
    this._authService = authService || AuthService;
    this._storyAPI = storyAPI || StoryAPI;

    this._page = 1;
    this._size = 12;
    this._hasMoreStories = true;
  }

  async init() {
    await this.loadStories();
  }

  async loadStories() {
    try {
      this._view.showLoading();

      // Get auth data
      let auth = this._authService.getAuth();

      // If no auth data, create a test token for demo purposes
      if (!auth || !auth.token) {
        console.log("No authentication token found, using test token");
        auth = {
          userId: "user-test",
          name: "Test User",
          token: "test-token",
        };
        // Save the test token
        this._authService.setAuth(auth);
      }

      console.log("Using auth token:", auth.token);

      // For testing purposes, use mock data if we're using the test token
      if (auth.token === "test-token") {
        console.log("Using mock data for testing");
        const mockStories = this._generateMockStories();

        // Save mock stories to IndexedDB
        await StoryIdb.saveStories(mockStories);

        // Show mock stories
        this._view.showStories(mockStories);
        this._view.updatePagination({
          currentPage: this._page,
          hasMoreStories: false,
        });

        this._view.hideLoading();
        return;
      }

      try {
        // Set a timeout to prevent infinite loading
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Request timeout")), 5000);
        });

        // Try to fetch stories from API with pagination
        const fetchPromise = this._storyAPI.getStories(auth.token, {
          page: this._page,
          size: this._size,
          location: 0, // Always don't show location in home page
        });

        // Race between fetch and timeout
        const response = await Promise.race([fetchPromise, timeoutPromise]);

        if (response.error === false) {
          // Store stories
          const stories = response.listStory || [];

          // Save stories to IndexedDB for offline use
          try {
            await StoryIdb.saveStories(stories);
            console.log("Stories saved to IndexedDB:", stories.length);
          } catch (dbError) {
            console.error("Error saving to IndexedDB:", dbError);
          }

          // Check if there are more stories
          this._hasMoreStories = stories.length === this._size;

          if (stories.length === 0) {
            this._view.showEmptyStories();
          } else {
            this._view.showStories(stories);
          }

          this._view.updatePagination({
            currentPage: this._page,
            hasMoreStories: this._hasMoreStories,
          });
        } else {
          throw new Error(response.message || "Failed to load stories");
        }
      } catch (error) {
        console.error("Error loading stories from API:", error);

        // If API request fails, try to load from IndexedDB
        console.log("Trying to load stories from IndexedDB...");
        try {
          const stories = await StoryIdb.getAllStories();

          if (stories && stories.length > 0) {
            console.log(
              "Successfully loaded stories from IndexedDB:",
              stories.length
            );
            this._view.showStories(stories);
            this._view.updatePagination({
              currentPage: this._page,
              hasMoreStories: false, // Assume no more stories in offline mode
            });
          } else {
            console.log(
              "No stories found in IndexedDB, generating mock stories"
            );
            // If no stories in IndexedDB, generate mock stories
            const mockStories = this._generateMockStories();

            // Save mock stories to IndexedDB
            try {
              await StoryIdb.saveStories(mockStories);
              console.log("Mock stories saved to IndexedDB");
            } catch (saveError) {
              console.error("Error saving mock stories:", saveError);
            }

            this._view.showStories(mockStories);
            this._view.updatePagination({
              currentPage: this._page,
              hasMoreStories: false,
            });
          }
        } catch (dbError) {
          console.error("Error accessing IndexedDB:", dbError);
          // Last resort: show mock stories directly
          const mockStories = this._generateMockStories();
          this._view.showStories(mockStories);
          this._view.updatePagination({
            currentPage: 1,
            hasMoreStories: false,
          });
        }
      }

      this._view.hideLoading();
    } catch (error) {
      console.error("Error in loadStories:", error);
      this._view.hideLoading();
      this._view.showError(error.message);
    }
  }

  setPage(page) {
    this._page = page;
  }

  // Size is fixed at 12

  getPage() {
    return this._page;
  }

  getSize() {
    return this._size;
  }

  hasMoreStories() {
    return this._hasMoreStories;
  }

  nextPage() {
    if (this._hasMoreStories) {
      this._page++;
      return true;
    }
    return false;
  }

  prevPage() {
    if (this._page > 1) {
      this._page--;
      return true;
    }
    return false;
  }

  // Generate mock stories for testing
  _generateMockStories() {
    const mockStories = [];

    // Create more varied mock stories
    const descriptions = [
      "This is a test story for offline testing. This story is generated for testing purposes.",
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies.",
      "Exploring the beautiful landscapes of Indonesia. The mountains and beaches are breathtaking!",
      "Just finished reading an amazing book about artificial intelligence and its impact on society.",
      "Attended a tech conference yesterday. Learned a lot about progressive web apps and service workers.",
      "Cooking a new recipe today. It's a traditional dish with a modern twist. Can't wait to taste it!",
      "Working on a new project using JavaScript and IndexedDB. It's challenging but fun!",
      "Visited a museum today. The art exhibition was fascinating and thought-provoking.",
    ];

    const names = [
      "Test User",
      "John Doe",
      "Jane Smith",
      "Alex Johnson",
      "Maria Garcia",
      "Budi Santoso",
      "Dewi Putri",
      "Ahmad Rahman",
    ];

    // Use real API image URLs for better offline testing
    // These URLs will be cached by the service worker
    const apiImages = [
      "https://picsum.photos/id/237/500/300", // Dog image
      "https://picsum.photos/id/1/500/300", // Laptop image
      "https://picsum.photos/id/20/500/300", // Mountain image
      "https://picsum.photos/id/30/500/300", // Cat image
    ];

    // Fallback local images if API images fail
    const localImages = [
      "/icons/icon-512x512.png",
      "/icons/icon-384x384.png",
      "/icons/icon-192x192.png",
      "/icons/icon-128x128.png",
    ];

    // Generate 8 mock stories
    for (let i = 1; i <= 8; i++) {
      const randomIndex = Math.floor(Math.random() * descriptions.length);
      const nameIndex = Math.floor(Math.random() * names.length);
      const imageIndex = i % apiImages.length;

      // Pre-cache the API images
      if ("caches" in window) {
        try {
          fetch(apiImages[imageIndex])
            .then((response) => {
              if (response.ok) {
                const clonedResponse = response.clone();
                caches.open("story-app-api-images").then((cache) => {
                  cache.put(apiImages[imageIndex], clonedResponse);
                  console.log(`Pre-cached API image: ${apiImages[imageIndex]}`);
                });
              }
            })
            .catch((error) =>
              console.log(`Failed to pre-cache image: ${error}`)
            );
        } catch (error) {
          console.error("Error pre-caching image:", error);
        }
      }

      mockStories.push({
        id: `story-${i}`,
        name: `${names[nameIndex]} ${i}`,
        description: descriptions[randomIndex],
        // Use API images with fallback to local images
        photoUrl: apiImages[imageIndex],
        photoUrlFallback: localImages[imageIndex], // Fallback image
        createdAt: new Date(Date.now() - i * 86400000).toISOString(), // Different dates
        lat: i % 2 === 0 ? -6.2088 + i * 0.01 : null,
        lon: i % 2 === 0 ? 106.8456 + i * 0.01 : null,
      });
    }

    return mockStories;
  }
}

export default HomePresenter;
