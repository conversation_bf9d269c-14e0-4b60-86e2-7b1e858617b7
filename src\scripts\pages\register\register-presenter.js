import AuthService from '../../utils/auth';
import StoryAPI from '../../data/api';

class RegisterPresenter {
  constructor({ view, authService, storyAPI }) {
    this._view = view;
    this._authService = authService || AuthService;
    this._storyAPI = storyAPI || StoryAPI;
  }

  async register(name, email, password) {
    try {
      // Basic validation
      if (!name || !email || !password) {
        this._view.showErrorMessage('Please fill in all fields');
        return false;
      }

      this._view.showLoading();

      // Call the register API
      const response = await this._storyAPI.register({
        name,
        email,
        password,
      });

      this._view.hideLoading();

      if (response.error === false) {
        // Registration successful
        this._view.showSuccessMessage('Registration successful! Please login.');
        return true;
      } else {
        // Handle registration failure
        this._view.showErrorMessage(response.message || 'Registration failed. Please try again.');
        return false;
      }
    } catch (error) {
      console.error('Error during registration:', error);
      this._view.hideLoading();
      this._view.showErrorMessage('Registration failed. Please try again later.');
      return false;
    }
  }
}

export default RegisterPresenter;
