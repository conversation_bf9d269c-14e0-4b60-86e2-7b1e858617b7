import LoginPresenter from "./login-presenter";

export default class Login {
  constructor() {
    this._presenter = new LoginPresenter({
      view: this,
    });
  }
  async render() {
    return `
        <div class="login-container">
          <h2>🔑 Login</h2>
          <form id="login-form">
            <div class="form-group">
              <label for="email">📧 Email</label>
              <input type="email" id="email" name="email" placeholder="Enter your email" required />
            </div>

            <div class="form-group">
              <label for="password">🔒 Password</label>
              <input type="password" id="password" name="password" placeholder="Enter your password" required />
            </div>

            <button type="submit" id="login-button">🔑 Login</button>
            <div id="loading-indicator" class="loading-indicator" style="display: none;">
              <div class="spinner"></div>
              <p>Logging in...</p>
            </div>
          </form>

          <p>Don't have an account? <a href="#/register">Register here</a></p>
        </div>
      `;
  }

  async afterRender() {
    // Select the form
    const form = document.getElementById("login-form");

    form.addEventListener("submit", async (event) => {
      event.preventDefault(); // Prevent form from submitting the default way

      const email = document.getElementById("email").value;
      const password = document.getElementById("password").value;

      // Use presenter to handle login
      const success = await this._presenter.login(email, password);

      if (success) {
        // Redirect to home page
        window.location.hash = "#/home";
      }
    });
  }

  // View interface methods for the presenter
  showLoading() {
    document.getElementById("login-button").style.display = "none";
    document.getElementById("loading-indicator").style.display = "flex";
  }

  hideLoading() {
    document.getElementById("login-button").style.display = "block";
    document.getElementById("loading-indicator").style.display = "none";
  }

  showSuccessMessage(message) {
    alert(message);
  }

  showErrorMessage(message) {
    alert(message);
  }
}
