import AuthService from '../../utils/auth';

class LogoutPresenter {
  constructor({ view, authService }) {
    this._view = view;
    this._authService = authService || AuthService;
  }

  logout() {
    // Clear authentication data
    this._authService.clearAuth();
    
    // Show logout message
    this._view.showLogoutMessage();
    
    // Return true to indicate successful logout
    return true;
  }
}

export default LogoutPresenter;
