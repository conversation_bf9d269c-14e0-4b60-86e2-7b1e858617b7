<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="<PERSON><PERSON>'s Story App - Share your stories with the world"
    />
    <link rel="shortcut icon" href="favicon.png" />
    <title><PERSON><PERSON>'s Story App</title>
    <meta name="view-transition" content="same-origin" />
    <meta name="theme-color" content="#2c5282" />
    <link rel="manifest" href="/manifest.json" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

    <style>
      /* Skip link styling for accessibility */
      .skip-link {
        position: absolute;
        top: -40px;
        left: 0;
        background: #000;
        color: #fff;
        padding: 8px 16px;
        z-index: 100;
        text-decoration: none;
        transition: top 0.3s ease;
      }

      .skip-link:focus {
        top: 0;
      }

      /* Ensure map containers are visible */
      .map-container, .map-container-fullscreen, .map-picker {
        height: 450px !important;
        width: 100% !important;
        border: 2px solid #4299e1 !important;
        border-radius: 10px !important;
        overflow: hidden !important;
        position: relative !important;
        z-index: 1 !important;
        margin-bottom: 20px !important;
      }

      .map-picker {
        height: 300px !important;
      }

      .map-container-fullscreen {
        height: 75vh !important;
      }

      /* Fix Leaflet controls */
      .leaflet-control-container {
        z-index: 1000 !important;
      }
    </style>
  </head>
  <body>
    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to content</a>

    <header role="banner">
      <div class="main-header container">
        <a class="brand-name" href="#/home">📚 Story App</a>

        <nav
          id="navigation-drawer"
          class="navigation-drawer"
          role="navigation"
          aria-label="Main Navigation"
        >
          <ul id="nav-list" class="nav-list">
            <li><a href="#/home">🏠 Home</a></li>
            <li><a href="#/add">📝 Add Story</a></li>
            <li><a href="#/maps">🗺️ Maps</a></li>
            <li class="nav-item-with-button">
              <a href="#/favorites">❤️ Favorites</a>
              <button
                id="notification-toggle-nav"
                class="notification-button"
                aria-label="Toggle notifications"
              >
                🔔
              </button>
            </li>
            <li><a href="#/logout">🚪 Logout</a></li>
          </ul>
        </nav>

        <button
          id="drawer-button"
          class="drawer-button"
          aria-label="Toggle navigation menu"
          aria-expanded="false"
          aria-controls="navigation-drawer"
        >
          ☰
        </button>
      </div>
    </header>

    <main id="main-content" class="main-content" role="main">
      <!-- Initial loading state -->
      <div class="container" id="initial-content">
        <h2 class="home-title">📰 Latest Stories</h2>
        <div style="text-align: center; margin: 2rem 0">
          <div class="spinner"></div>
          <p>Loading stories...</p>
        </div>
      </div>
    </main>

    <footer role="contentinfo" class="footer">
      <div class="container">
        <p>&copy; 2024 Gal's Story App. All rights reserved.</p>
      </div>
    </footer>

    <!-- Notification permission dialog -->
    <div
      id="notification-dialog"
      style="
        display: none;
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: white;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        max-width: 300px;
      "
    >
      <p style="margin-top: 0">Would you like to enable notifications?</p>
      <div
        style="
          display: flex;
          justify-content: flex-end;
          gap: 10px;
          margin-top: 10px;
        "
      >
        <button
          id="notification-deny"
          style="
            padding: 8px 12px;
            background: #e2e8f0;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          "
        >
          No thanks
        </button>
        <button
          id="notification-allow"
          style="
            padding: 8px 12px;
            background: #4299e1;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          "
        >
          Enable
        </button>
      </div>
    </div>
  </body>
</html>
