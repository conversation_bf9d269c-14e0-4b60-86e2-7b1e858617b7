import "../../components/notification-settings";
import { showFormattedDate } from "../../utils/index";
import HomePresenter from "./home-presenter";

export default class HomePage {
  constructor() {
    this.stories = [];
    this.isLoading = false;
    this._presenter = new HomePresenter({
      view: this,
    });
  }

  async render() {
    return `
      <section class="container">
        <h2 class="home-title">📰 Latest Stories</h2>

        <!-- Notification Settings Component -->
        <notification-settings></notification-settings>

        <div id="loading-stories" class="loading-indicator" style="display: none; justify-content: center; margin: 2rem 0;">
          <div class="spinner"></div>
          <p>Loading stories...</p>
        </div>

        <div id="stories-container" class="story-list">
          <!-- Stories will be loaded here -->
        </div>

        <div id="error-container" style="display: none; text-align: center; color: red; margin: 2rem 0;">
          <p>Failed to load stories. Please try again later.</p>
        </div>

        <div class="pagination-controls">
          <button id="prev-page" class="pagination-btn" disabled>Previous</button>
          <span id="page-info">Page <span id="current-page">1</span></span>
          <button id="next-page" class="pagination-btn">Next</button>
        </div>

      </section>

      <!-- Add Story Floating Action Button (outside container for proper fixed positioning) -->
      <a href="#/add" id="add-story-fab" class="add-story-fab" aria-label="Add new story" title="Add new story">
        <i class="fas fa-plus"></i>
        <span class="fab-tooltip">Add Story</span>
      </a>
    `;
  }

  async afterRender() {
    try {
      // Get DOM elements
      const prevPageBtn = document.getElementById("prev-page");
      const nextPageBtn = document.getElementById("next-page");
      const currentPageSpan = document.getElementById("current-page");
      const storiesContainer = document.getElementById("stories-container");

      // Show a loading message immediately
      storiesContainer.innerHTML = `
        <div style="text-align: center; margin: 2rem 0;">
          <p>Loading stories...</p>
        </div>
      `;
      storiesContainer.style.display = "block";

      // Set up event listeners

      // We don't need to add a click handler for the FAB button
      // since it's already an <a> element with href="#/add"
      // This was causing a conflict with the default behavior

      prevPageBtn.addEventListener("click", async () => {
        try {
          if (this._presenter.prevPage()) {
            await this._presenter.loadStories();
            currentPageSpan.textContent = this._presenter.getPage();
            prevPageBtn.disabled = this._presenter.getPage() === 1;
          }
        } catch (error) {
          console.error("Error navigating to previous page:", error);
          this.showError("Failed to load previous page. Please try again.");
        }
      });

      nextPageBtn.addEventListener("click", async () => {
        try {
          if (this._presenter.nextPage()) {
            await this._presenter.loadStories();
            currentPageSpan.textContent = this._presenter.getPage();
            prevPageBtn.disabled = false;
          }
        } catch (error) {
          console.error("Error navigating to next page:", error);
          this.showError("Failed to load next page. Please try again.");
        }
      });

      // Initial load with error handling
      try {
        await this._presenter.init();
      } catch (error) {
        console.error("Error initializing home page:", error);
        this.showError(
          "Failed to load stories. Please try refreshing the page."
        );

        // Create some fallback content if everything fails
        if (this.stories.length === 0) {
          this.showEmptyStories();
        }
      }
    } catch (error) {
      console.error("Fatal error in home page afterRender:", error);
      // Last resort error handling
      const mainContent = document.querySelector("#main-content");
      if (mainContent) {
        mainContent.innerHTML = `
          <section class="container">
            <h2 class="home-title">📰 Latest Stories</h2>
            <div style="text-align: center; margin: 2rem 0;">
              <p>Something went wrong while loading stories.</p>
              <button onclick="window.location.reload()" class="back-button">Refresh Page</button>
            </div>
          </section>
        `;
      }
    }
  }

  // View interface methods for the presenter
  showLoading() {
    this.isLoading = true;
    document.getElementById("loading-stories").style.display = "flex";
    document.getElementById("stories-container").style.display = "none";
    document.getElementById("error-container").style.display = "none";
  }

  hideLoading() {
    this.isLoading = false;
    document.getElementById("loading-stories").style.display = "none";
  }

  showStories(stories) {
    this.stories = stories;
    const storiesContainer = document.getElementById("stories-container");

    // Pre-cache all story images for offline use
    this.stories.forEach((story) => {
      if (story.photoUrl) {
        // Create an image element to preload the image
        const img = new Image();
        img.onload = () => {
          console.log(`Image loaded successfully: ${story.photoUrl}`);

          // Force the browser to cache the image
          if ("caches" in window) {
            fetch(story.photoUrl, { mode: "no-cors" })
              .then((response) => {
                if (response) {
                  caches.open("story-app-images").then((cache) => {
                    cache.put(story.photoUrl, response);
                    console.log(`Image cached successfully: ${story.photoUrl}`);
                  });
                }
              })
              .catch((error) =>
                console.error(`Failed to cache image: ${error}`)
              );
          }
        };

        img.onerror = () => {
          console.error(`Failed to load image: ${story.photoUrl}`);
          // If loading fails, try to use the fallback image
          if (story.photoUrlFallback) {
            console.log(`Using fallback image: ${story.photoUrlFallback}`);
          }
        };

        // Start loading the image
        img.src = story.photoUrl;
      }
    });

    // Render stories
    storiesContainer.innerHTML = this.stories
      .map((story) => {
        // Determine fallback image path
        const fallbackImage =
          story.photoUrlFallback || "/images/placeholder.svg";

        return `
      <article class="story-card">
        <a href="#/stories/${
          story.id
        }" class="story-link" aria-labelledby="story-title-${story.id}">
          <img src="${story.photoUrl}"
               alt="Story image by ${story.name}: ${story.description.substring(
          0,
          50
        )}${story.description.length > 50 ? "..." : ""}"
               class="story-img"
               loading="eager"
               data-fallback="${fallbackImage}"
               onerror="this.onerror=null; this.src=this.getAttribute('data-fallback'); console.log('Image failed to load, using fallback: ' + this.getAttribute('data-fallback'));"
               />
          <div class="story-content">
            <h3 id="story-title-${story.id}" class="story-name">${
          story.name
        }</h3>
            <p class="story-description">${story.description}</p>
            <time class="story-date" datetime="${new Date(
              story.createdAt
            ).toISOString()}">${showFormattedDate(story.createdAt)}</time>
            ${
              story.lat && story.lon
                ? `
              <div class="story-location">
                <small aria-label="Location coordinates">📍 Location: ${story.lat.toFixed(
                  4
                )}, ${story.lon.toFixed(4)}</small>
              </div>
            `
                : ""
            }
            <div class="view-detail">
              <span>View Detail</span>
            </div>
          </div>
        </a>
      </article>
    `;
      })
      .join("");

    // Show stories container
    storiesContainer.style.display = "grid";

    // Add event listeners to handle image loading errors with better fallbacks
    setTimeout(() => {
      document.querySelectorAll(".story-img").forEach((img) => {
        // Force reload the image to ensure it's loaded from cache if available
        const originalSrc = img.src;
        img.src = "";
        img.src = originalSrc;

        // Add error handler
        img.addEventListener("error", function () {
          console.log(`Image error detected for: ${this.src}`);
          const fallbackSrc =
            this.getAttribute("data-fallback") || "/images/placeholder.svg";

          // Use the fallback image
          if (this.src !== fallbackSrc) {
            console.log(`Switching to fallback image: ${fallbackSrc}`);
            this.src = fallbackSrc;
          }
        });
      });
    }, 500); // Small delay to ensure DOM is fully processed
  }

  showEmptyStories() {
    const storiesContainer = document.getElementById("stories-container");
    storiesContainer.innerHTML =
      '<p style="text-align: center; margin: 2rem 0;">No stories found. Be the first to add a story!</p>';
    storiesContainer.style.display = "block";
  }

  showError(message) {
    this.isLoading = false;
    document.getElementById("loading-stories").style.display = "none";
    const errorContainer = document.getElementById("error-container");
    // Optionally display the error message
    if (message) {
      errorContainer.querySelector("p").textContent = message;
    }
    errorContainer.style.display = "block";
  }

  updatePagination({ currentPage, hasMoreStories }) {
    document.getElementById("current-page").textContent = currentPage;
    document.getElementById("prev-page").disabled = currentPage === 1;
    document.getElementById("next-page").disabled = !hasMoreStories;
  }
}
