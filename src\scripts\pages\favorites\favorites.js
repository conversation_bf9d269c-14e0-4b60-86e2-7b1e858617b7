import { showFormattedDate } from "../../utils/index";
import FavoritesPresenter from "./favorites-presenter";

export default class FavoritesPage {
  constructor() {
    this.stories = [];
    this.isLoading = false;
    this._presenter = new FavoritesPresenter({
      view: this,
    });
  }

  async render() {
    return `
      <section class="container">
        <h2 class="home-title">❤️ Favorites</h2>

        <div id="loading-favorites" class="loading-indicator" style="display: none; justify-content: center; margin: 2rem 0;">
          <div class="spinner"></div>
          <p>Loading favorite stories...</p>
        </div>

        <div id="favorites-container" class="story-list">
          <!-- Favorite stories will be loaded here -->
        </div>

        <div id="error-container" style="display: none; text-align: center; color: red; margin: 2rem 0;">
          <p>Failed to load favorite stories. Please try again later.</p>
        </div>

        <div id="empty-favorites" style="display: none; text-align: center; margin: 2rem 0;">
          <p>You don't have any favorite stories yet. Browse stories and add them to your favorites!</p>
          <a href="#/home" class="back-button" style="margin-top: 1rem; display: inline-block;">Browse Stories</a>
        </div>

        <div id="clear-favorites-container" style="text-align: center; margin: 2rem 0; display: none;">
          <button id="clear-favorites-button" class="pagination-btn" style="background-color: #e53e3e;">Clear All Favorites</button>
        </div>
      </section>
    `;
  }

  async afterRender() {
    // Initialize the presenter
    await this._presenter.init();

    // Set up event listener for clear favorites button
    const clearFavoritesButton = document.getElementById(
      "clear-favorites-button"
    );
    if (clearFavoritesButton) {
      clearFavoritesButton.addEventListener("click", async () => {
        if (
          confirm("Are you sure you want to clear all your favorite stories?")
        ) {
          await this._presenter.clearFavorites();
        }
      });
    }
  }

  // View interface methods for the presenter
  showLoading() {
    this.isLoading = true;
    document.getElementById("loading-favorites").style.display = "flex";
    document.getElementById("favorites-container").style.display = "none";
    document.getElementById("error-container").style.display = "none";
    document.getElementById("empty-favorites").style.display = "none";
    document.getElementById("clear-favorites-container").style.display = "none";
  }

  hideLoading() {
    this.isLoading = false;
    document.getElementById("loading-favorites").style.display = "none";
  }

  showFavorites(stories) {
    this.stories = stories;
    const favoritesContainer = document.getElementById("favorites-container");

    // Pre-cache all favorite story images for offline use
    this.preCacheFavoriteImages(stories);

    // Render stories
    favoritesContainer.innerHTML = this.stories
      .map(
        (story) => `
      <article class="story-card">
        <a href="#/stories/${
          story.id
        }" class="story-link" aria-labelledby="story-title-${story.id}">
          <img src="${story.photoUrl}"
               alt="Story image by ${story.name}: ${story.description.substring(
          0,
          50
        )}${story.description.length > 50 ? "..." : ""}"
               class="story-img"
               loading="eager"
               data-fallback="/images/placeholder.svg"
               data-id="${story.id}"
               onerror="this.onerror=null; this.src=this.getAttribute('data-fallback'); console.log('Image failed to load, using fallback: ' + this.getAttribute('data-fallback'));" />
          <div class="story-content">
            <h3 id="story-title-${story.id}" class="story-name">${
          story.name
        }</h3>
            <p class="story-description">${story.description}</p>
            <time class="story-date" datetime="${new Date(
              story.createdAt
            ).toISOString()}">${showFormattedDate(story.createdAt)}</time>
            ${
              story.lat && story.lon
                ? `
              <div class="story-location">
                <small aria-label="Location coordinates">📍 Location: ${story.lat.toFixed(
                  4
                )}, ${story.lon.toFixed(4)}</small>
              </div>
            `
                : ""
            }
            <div class="view-detail">
              <span>View Detail</span>
            </div>
          </div>
        </a>
      </article>
    `
      )
      .join("");

    // Show favorites container and clear button
    favoritesContainer.style.display = "grid";
    document.getElementById("clear-favorites-container").style.display =
      "block";

    // Add event listeners to handle image loading errors with better fallbacks
    setTimeout(() => {
      document.querySelectorAll(".story-img").forEach((img) => {
        // Force reload the image to ensure it's loaded from cache if available
        const originalSrc = img.src;
        img.src = "";
        img.src = originalSrc;

        // Add error handler
        img.addEventListener("error", function () {
          console.log(`Image error detected for: ${this.src}`);
          const fallbackSrc =
            this.getAttribute("data-fallback") || "/images/placeholder.svg";

          // Try to find the image in cache
          if ("caches" in window) {
            caches
              .match(originalSrc)
              .then((response) => {
                if (response) {
                  // If found in cache, use it
                  console.log(`Found image in cache: ${originalSrc}`);
                  return response.blob();
                } else {
                  // If not found, try to find a picsum image
                  return caches
                    .open("story-app-picsum-images")
                    .then((cache) => cache.keys())
                    .then((keys) => {
                      if (keys.length > 0) {
                        const randomIndex = Math.floor(
                          Math.random() * keys.length
                        );
                        return caches.match(keys[randomIndex].url);
                      }
                      return null;
                    })
                    .then((response) => (response ? response.blob() : null));
                }
              })
              .then((blob) => {
                if (blob) {
                  const objectURL = URL.createObjectURL(blob);
                  this.src = objectURL;
                } else {
                  // Use the fallback image
                  this.src = fallbackSrc;
                }
              })
              .catch((error) => {
                console.error("Error retrieving image from cache:", error);
                this.src = fallbackSrc;
              });
          } else {
            // If caches API not available, use fallback
            this.src = fallbackSrc;
          }
        });
      });
    }, 500); // Small delay to ensure DOM is fully processed
  }

  // Method to pre-cache favorite story images
  preCacheFavoriteImages(stories) {
    if (!("caches" in window)) {
      console.log("Cache API not available");
      return;
    }

    try {
      console.log("Pre-caching favorite story images...");

      // Open the cache
      caches.open("story-app-favorites-images").then((cache) => {
        // Cache each story image
        stories.forEach((story) => {
          if (story.photoUrl) {
            // Check if already in cache
            cache.match(story.photoUrl).then((match) => {
              if (match) {
                console.log(`Favorite image already cached: ${story.photoUrl}`);
                return;
              }

              // Create an image element to preload the image
              const img = new Image();
              img.onload = () => {
                console.log(
                  `Favorite image loaded successfully: ${story.photoUrl}`
                );

                // Force the browser to cache the image
                fetch(story.photoUrl, { mode: "no-cors" })
                  .then((response) => {
                    if (response) {
                      cache.put(story.photoUrl, response);
                      console.log(
                        `Favorite image cached successfully: ${story.photoUrl}`
                      );
                    }
                  })
                  .catch((error) =>
                    console.error(`Failed to cache favorite image: ${error}`)
                  );
              };

              img.onerror = () => {
                console.error(
                  `Failed to load favorite image: ${story.photoUrl}`
                );

                // If loading fails, try to use a picsum image as fallback
                caches.open("story-app-picsum-images").then((picsumCache) => {
                  picsumCache.keys().then((keys) => {
                    if (keys.length > 0) {
                      const randomIndex = Math.floor(
                        Math.random() * keys.length
                      );
                      picsumCache
                        .match(keys[randomIndex].url)
                        .then((response) => {
                          if (response) {
                            cache.put(story.photoUrl, response);
                            console.log(
                              `Using picsum image as fallback for favorite: ${keys[randomIndex].url}`
                            );
                          }
                        });
                    }
                  });
                });
              };

              // Start loading the image
              img.src = story.photoUrl;
            });
          }
        });
      });
    } catch (error) {
      console.error("Error pre-caching favorite images:", error);
    }
  }

  showEmptyFavorites() {
    document.getElementById("empty-favorites").style.display = "block";
    document.getElementById("favorites-container").style.display = "none";
    document.getElementById("clear-favorites-container").style.display = "none";
  }

  showError(message) {
    this.isLoading = false;
    document.getElementById("loading-favorites").style.display = "none";
    const errorContainer = document.getElementById("error-container");
    if (message) {
      errorContainer.querySelector("p").textContent = message;
    }
    errorContainer.style.display = "block";
  }
}
