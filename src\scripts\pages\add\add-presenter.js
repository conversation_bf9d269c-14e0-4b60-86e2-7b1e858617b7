import StoryAPI from "../../data/api";
import AuthService from "../../utils/auth";

class AddPresenter {
  constructor({ view, authService, storyAPI }) {
    this._view = view;
    this._authService = authService || AuthService;
    this._storyAPI = storyAPI || StoryAPI;
  }

  async submitStory(storyData) {
    try {
      this._view.showLoading();

      // Get auth data
      const auth = this._authService.getAuth();

      if (!auth || !auth.token) {
        throw new Error("Authentication token not found");
      }

      // Submit story
      const response = await this._storyAPI.addStory(storyData, auth.token);

      this._view.hideLoading();

      if (response.error === false) {
        this._view.showSuccessMessage("Story submitted successfully!");

        // Show push notification if subscribed
        if (
          window.notificationManager &&
          (await window.notificationManager.isSubscribed())
        ) {
          console.log("User is subscribed to push notifications");
          // The notification will be sent by the server automatically
          // when a story is created by a subscribed user
        }

        return true;
      } else {
        throw new Error(response.message || "Failed to submit story.");
      }
    } catch (error) {
      console.error("Error submitting story:", error);
      this._view.hideLoading();
      this._view.showErrorMessage(
        error.message || "Failed to submit story. Please try again later."
      );
      return false;
    }
  }

  async submitGuestStory(storyData) {
    try {
      this._view.showLoading();

      // Submit story as guest
      const response = await this._storyAPI.addGuestStory(storyData);

      this._view.hideLoading();

      if (response.error === false) {
        this._view.showSuccessMessage("Story submitted as guest successfully!");
        return true;
      } else {
        throw new Error(response.message || "Failed to submit story as guest.");
      }
    } catch (error) {
      console.error("Error submitting guest story:", error);
      this._view.hideLoading();
      this._view.showErrorMessage(
        error.message ||
          "Failed to submit story as guest. Please try again later."
      );
      return false;
    }
  }
}

export default AddPresenter;
