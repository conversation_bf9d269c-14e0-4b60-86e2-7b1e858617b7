import L from "leaflet";
import "leaflet/dist/leaflet.css";
import AddPresenter from "./add-presenter";

export default class Add {
  constructor() {
    this.stream = null;
    this.marker = null;
    this._presenter = new AddPresenter({
      view: this,
    });
  }

  // Cleanup method to stop camera when navigating away
  cleanup() {
    if (this.stream) {
      this.stream.getTracks().forEach((track) => track.stop());
      this.stream = null;

      // Clear video source if element exists
      const videoElement = document.getElementById("camera-stream");
      if (videoElement) {
        videoElement.srcObject = null;
      }

      console.log("Camera stopped during page navigation");
    }
  }

  // Render the HTML content asynchronously
  async render() {
    return `
      <div class="container">
        <h2 class="home-title" id="form-title">📝 Add New Story</h2>
        <form class="add-form" aria-labelledby="form-title">
          <fieldset>
            <legend>📸 Photo Upload</legend>
            <div class="photo-input-section">
              <div class="form-group">
                <label for="photo-file">📤 Upload Photo (.jpg, .jpeg, .png only)</label>
                <input type="file" id="photo-file" accept=".jpg, .jpeg, .png" aria-describedby="file-format-hint" />
                <span id="file-format-hint" class="form-hint">Accepted formats: JPG, JPEG, PNG. Maximum size: 1MB</span>
              </div>

              <div class="or-divider">
                <span>OR</span>
              </div>

              <div class="camera-section">
                <button type="button" id="start-camera" aria-controls="camera-container" aria-expanded="false">📸 Start Camera</button>
                <div id="camera-container" style="display: none;" aria-live="polite">
                  <video id="camera-stream" playsinline aria-label="Camera preview"></video>
                  <canvas id="snapshot" style="display: none;"></canvas>
                  <div class="camera-controls">
                    <button type="button" id="take-photo">📷 Take Photo</button>
                    <button type="button" id="stop-camera" class="secondary-button">⏹️ Stop Camera</button>
                  </div>
                </div>
              </div>
            </div>

            <input type="hidden" id="photo-data" />
            <div id="photo-preview" class="photo-preview" style="display: none;" aria-live="polite">
              <img id="preview-image" src="" alt="Preview of your photo" />
              <button type="button" id="remove-photo" title="Remove photo" aria-label="Remove photo">❌</button>
            </div>
          </fieldset>

          <div class="form-group">
            <label for="description">📄 Description <span class="required" aria-hidden="true">*</span></label>
            <textarea id="description" name="description" rows="6" required placeholder="Write a story..." aria-required="true"></textarea>
            <span class="form-hint">Required field</span>
          </div>

          <fieldset>
            <legend>📍 Location (Optional)</legend>
            <div id="map" class="map-picker" style="height: 300px; margin-bottom: 1rem;" aria-label="Map for selecting location"></div>
            <input type="hidden" id="lat" name="lat" />
            <input type="hidden" id="lng" name="lon" />
          </fieldset>

          <div class="button-group">
            <button type="submit" id="submit-button" class="primary-button">✅ Submit Story</button>
            <button type="button" id="cancel-button" class="secondary-button">❌ Cancel</button>
          </div>
          <div id="loading-indicator" class="loading-indicator" style="display: none;" aria-live="assertive" role="status">
            <div class="spinner" aria-hidden="true"></div>
            <p>Submitting story...</p>
          </div>
        </form>
      </div>
    `;
  }

  // Perform additional setup after render
  async afterRender() {
    this.setupCamera();
    this.setupFileUpload();
    this.setupPhotoPreview();
    this.setupMap();
    this.setupFormSubmit();
  }

  // === CAMERA SETUP ===
  setupCamera() {
    const startCameraBtn = document.getElementById("start-camera");
    const stopCameraBtn = document.getElementById("stop-camera");
    const cameraContainer = document.getElementById("camera-container");
    const video = document.getElementById("camera-stream");
    const canvas = document.getElementById("snapshot");
    const takePhotoBtn = document.getElementById("take-photo");
    const photoDataInput = document.getElementById("photo-data");
    const photoPreview = document.getElementById("photo-preview");
    const previewImage = document.getElementById("preview-image");

    // Start camera button click handler
    startCameraBtn.addEventListener("click", () => {
      startCameraBtn.disabled = true;
      startCameraBtn.textContent = "Starting camera...";
      startCameraBtn.setAttribute("aria-expanded", "true");

      navigator.mediaDevices
        .getUserMedia({ video: true })
        .then((mediaStream) => {
          this.stream = mediaStream;
          video.srcObject = this.stream;

          // Show camera container
          cameraContainer.style.display = "block";
          startCameraBtn.style.display = "none";

          // Start playing the video
          video.play();

          // Announce to screen readers
          const statusElement = document.createElement("div");
          statusElement.className = "visually-hidden";
          statusElement.setAttribute("aria-live", "polite");
          statusElement.textContent =
            "Camera is now active. You can take a photo.";
          document.body.appendChild(statusElement);

          // Remove after announcement
          setTimeout(() => {
            document.body.removeChild(statusElement);
          }, 1000);
        })
        .catch((err) => {
          console.error("Cannot access camera:", err);
          alert(
            "Cannot access camera. Please make sure your camera is connected and you have given permission to use it."
          );
          startCameraBtn.disabled = false;
          startCameraBtn.textContent = "Start Camera";
          startCameraBtn.setAttribute("aria-expanded", "false");
        });
    });

    // Stop camera button click handler
    stopCameraBtn.addEventListener("click", () => {
      if (this.stream) {
        this.stream.getTracks().forEach((track) => track.stop());
        this.stream = null;
        video.srcObject = null;
      }

      // Hide camera container and show start button
      cameraContainer.style.display = "none";
      startCameraBtn.style.display = "inline-block";
      startCameraBtn.disabled = false;
      startCameraBtn.textContent = "Start Camera";
      startCameraBtn.setAttribute("aria-expanded", "false");

      // Announce to screen readers
      const statusElement = document.createElement("div");
      statusElement.className = "visually-hidden";
      statusElement.setAttribute("aria-live", "polite");
      statusElement.textContent = "Camera has been stopped.";
      document.body.appendChild(statusElement);

      // Remove after announcement
      setTimeout(() => {
        document.body.removeChild(statusElement);
      }, 1000);
    });

    // Take photo button click handler
    takePhotoBtn.addEventListener("click", () => {
      if (!this.stream) return;

      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      canvas.getContext("2d").drawImage(video, 0, 0);
      const dataUrl = canvas.toDataURL("image/jpeg");
      photoDataInput.value = dataUrl;

      // Show preview
      previewImage.src = dataUrl;
      photoPreview.style.display = "block";

      // Clear file input
      document.getElementById("photo-file").value = "";

      // Stop the camera after taking photo
      if (this.stream) {
        this.stream.getTracks().forEach((track) => track.stop());
        this.stream = null;
        video.srcObject = null;
      }

      // Hide camera container and show start button
      cameraContainer.style.display = "none";
      startCameraBtn.style.display = "inline-block";
      startCameraBtn.disabled = false;
      startCameraBtn.textContent = "Start Camera";
      startCameraBtn.setAttribute("aria-expanded", "false");

      // Announce to screen readers
      const statusElement = document.createElement("div");
      statusElement.className = "visually-hidden";
      statusElement.setAttribute("aria-live", "polite");
      statusElement.textContent = "Photo captured and ready for submission.";
      document.body.appendChild(statusElement);

      // Remove after announcement
      setTimeout(() => {
        document.body.removeChild(statusElement);
      }, 1000);
    });
  }

  // === FILE UPLOAD SETUP ===
  setupFileUpload() {
    const fileInput = document.getElementById("photo-file");
    const photoDataInput = document.getElementById("photo-data");
    const photoPreview = document.getElementById("photo-preview");
    const previewImage = document.getElementById("preview-image");

    fileInput.addEventListener("change", (event) => {
      const file = event.target.files[0];

      if (file) {
        // Check file size (max 1MB)
        if (file.size > 1024 * 1024) {
          alert("File size exceeds 1MB. Please choose a smaller image.");
          fileInput.value = "";
          return;
        }

        // Check file extension
        const validExtensions = ["jpg", "jpeg", "png"];
        const fileName = file.name.toLowerCase();
        const fileExtension = fileName.split(".").pop();

        if (!validExtensions.includes(fileExtension)) {
          alert("Please select a valid image file (.jpg, .jpeg, or .png).");
          fileInput.value = "";
          return;
        }

        // Read the file and create a data URL
        const reader = new FileReader();
        reader.onload = (e) => {
          const dataUrl = e.target.result;
          photoDataInput.value = dataUrl;

          // Show preview
          previewImage.src = dataUrl;
          photoPreview.style.display = "block";

          // Announce to screen readers
          const statusElement = document.createElement("div");
          statusElement.className = "visually-hidden";
          statusElement.setAttribute("aria-live", "polite");
          statusElement.textContent =
            "Photo uploaded and ready for submission.";
          document.body.appendChild(statusElement);

          // Remove after announcement
          setTimeout(() => {
            document.body.removeChild(statusElement);
          }, 1000);

          // Stop camera if it's running
          if (this.stream) {
            this.stream.getTracks().forEach((track) => track.stop());
            document.getElementById("camera-stream").srcObject = null;

            // Hide camera container and show start button
            document.getElementById("camera-container").style.display = "none";
            const startCameraBtn = document.getElementById("start-camera");
            startCameraBtn.style.display = "inline-block";
            startCameraBtn.disabled = false;
            startCameraBtn.textContent = "Start Camera";
            startCameraBtn.setAttribute("aria-expanded", "false");
          }
        };
        reader.readAsDataURL(file);
      }
    });
  }

  // === PHOTO PREVIEW SETUP ===
  setupPhotoPreview() {
    const removePhotoBtn = document.getElementById("remove-photo");
    const photoPreview = document.getElementById("photo-preview");
    const photoDataInput = document.getElementById("photo-data");
    const fileInput = document.getElementById("photo-file");

    removePhotoBtn.addEventListener("click", () => {
      // Clear photo data
      photoDataInput.value = "";
      photoPreview.style.display = "none";
      fileInput.value = "";

      // Make sure camera is stopped
      if (this.stream) {
        this.stream.getTracks().forEach((track) => track.stop());
        this.stream = null;
        document.getElementById("camera-stream").srcObject = null;
      }

      // Reset camera UI
      document.getElementById("camera-container").style.display = "none";
      const startCameraBtn = document.getElementById("start-camera");
      startCameraBtn.style.display = "inline-block";
      startCameraBtn.disabled = false;
      startCameraBtn.textContent = "Start Camera";
      startCameraBtn.setAttribute("aria-expanded", "false");

      // Announce to screen readers
      const statusElement = document.createElement("div");
      statusElement.className = "visually-hidden";
      statusElement.setAttribute("aria-live", "polite");
      statusElement.textContent =
        "Photo removed. Please upload or take a new photo.";
      document.body.appendChild(statusElement);

      // Remove after announcement
      setTimeout(() => {
        document.body.removeChild(statusElement);
      }, 1000);
    });
  }

  // === MAP SETUP ===
  setupMap() {
    // Fix Leaflet's default icon path issues
    this.fixLeafletDefaultIconPath();

    // Inisialisasi peta tanpa marker default, tetap di Bintaro Sektor 9
    const map = L.map("map").setView([-6.2730848, 106.7125006], 13); // Bintaro Sektor 9 default

    // Tambahkan layer peta (OpenStreetMap)
    L.tileLayer(
      "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
      {
        attribution:
          '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
      }
    ).addTo(map);

    // Tambahkan instruksi untuk pengguna
    const instructionDiv = document.createElement("div");
    instructionDiv.className = "map-instruction";
    instructionDiv.innerHTML = "<p>📍 Klik pada peta untuk memilih lokasi</p>";
    document.getElementById("map").appendChild(instructionDiv);

    // Tambahkan tombol untuk menggunakan lokasi saat ini
    const locationButton = document.createElement("button");
    locationButton.type = "button";
    locationButton.className = "current-location-btn";
    locationButton.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 8px;">
        <path d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10zm0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6z"/>
      </svg>
      Gunakan Lokasi Saat Ini
    `;
    locationButton.style.position = "absolute";
    locationButton.style.bottom = "20px";
    locationButton.style.left = "10px";
    locationButton.style.zIndex = "1000";
    locationButton.style.backgroundColor = "#000";
    locationButton.style.color = "white";
    locationButton.style.border = "none";
    locationButton.style.borderRadius = "50px";
    locationButton.style.padding = "12px 24px";
    locationButton.style.fontSize = "1rem";
    locationButton.style.fontWeight = "600";
    locationButton.style.cursor = "pointer";
    locationButton.style.display = "flex";
    locationButton.style.alignItems = "center";
    locationButton.style.justifyContent = "center";
    locationButton.style.boxShadow = "0 4px 8px rgba(0, 0, 0, 0.2)";
    document.getElementById("map").appendChild(locationButton);

    // Tambahkan event listener untuk tombol lokasi saat ini
    locationButton.addEventListener("click", () => {
      if (navigator.geolocation) {
        locationButton.disabled = true;
        locationButton.innerHTML = `
          <div class="spinner" style="width: 16px; height: 16px; margin-right: 8px;"></div>
          Mencari lokasi...
        `;

        navigator.geolocation.getCurrentPosition(
          (position) => {
            const { latitude: lat, longitude: lng } = position.coords;

            // Pindahkan peta ke lokasi saat ini
            map.setView([lat, lng], 15);

            // Update nilai input hidden
            document.getElementById("lat").value = lat.toFixed(6);
            document.getElementById("lng").value = lng.toFixed(6);

            // Tampilkan koordinat yang dipilih
            const locationInfo = document.createElement("div");
            locationInfo.className = "location-selected";
            locationInfo.style.left = "auto";
            locationInfo.style.right = "10px";
            locationInfo.innerHTML = `<p>Lokasi dipilih: ${lat.toFixed(
              6
            )}, ${lng.toFixed(6)}</p>`;

            // Hapus info lokasi sebelumnya jika ada
            const oldInfo = document.querySelector(".location-selected");
            if (oldInfo) {
              oldInfo.remove();
            }

            document.getElementById("map").appendChild(locationInfo);

            // Update marker
            if (this.marker) {
              this.marker.setLatLng([lat, lng]);
            } else {
              // Buat marker dengan ikon yang lebih besar dan berwarna
              const customIcon = L.icon({
                iconUrl:
                  "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png",
                iconSize: [30, 45],
                iconAnchor: [15, 45],
                popupAnchor: [0, -45],
              });

              this.marker = L.marker([lat, lng], {
                icon: customIcon,
                draggable: true,
              }).addTo(map);

              // Tambahkan event listener untuk drag marker
              this.marker.on("dragend", (event) => {
                const marker = event.target;
                const position = marker.getLatLng();

                // Update nilai input hidden
                document.getElementById("lat").value = position.lat.toFixed(6);
                document.getElementById("lng").value = position.lng.toFixed(6);

                // Update info lokasi
                const locationInfo =
                  document.querySelector(".location-selected");
                if (locationInfo) {
                  locationInfo.style.left = "auto";
                  locationInfo.style.right = "10px";
                  locationInfo.innerHTML = `<p>Lokasi dipilih: ${position.lat.toFixed(
                    6
                  )}, ${position.lng.toFixed(6)}</p>`;
                }
              });
            }

            // Reset tombol
            locationButton.disabled = false;
            locationButton.innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 8px;">
                <path d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10zm0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6z"/>
              </svg>
              Gunakan Lokasi Saat Ini
            `;
          },
          (error) => {
            console.error("Error getting location:", error);
            alert(
              "Tidak dapat mengakses lokasi Anda. Pastikan Anda telah memberikan izin lokasi."
            );
            locationButton.disabled = false;
            locationButton.innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 8px;">
                <path d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10zm0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6z"/>
              </svg>
              Gunakan Lokasi Saat Ini
            `;
          },
          { enableHighAccuracy: true, timeout: 5000, maximumAge: 0 }
        );
      } else {
        alert("Geolocation tidak didukung oleh browser Anda.");
      }
    });

    // Tambahkan event listener untuk klik pada peta
    let marker;
    let popup = L.popup();

    map.on("click", async (e) => {
      const { lat, lng } = e.latlng;

      // Update nilai input hidden
      document.getElementById("lat").value = lat.toFixed(6);
      document.getElementById("lng").value = lng.toFixed(6);

      // Tampilkan koordinat yang dipilih
      const locationInfo = document.createElement("div");
      locationInfo.className = "location-selected";
      locationInfo.style.left = "auto";
      locationInfo.style.right = "10px";
      locationInfo.innerHTML = `<p>Lokasi dipilih: ${lat.toFixed(
        6
      )}, ${lng.toFixed(6)}</p>`;

      // Hapus info lokasi sebelumnya jika ada
      const oldInfo = document.querySelector(".location-selected");
      if (oldInfo) oldInfo.remove();

      document.getElementById("map").appendChild(locationInfo);

      // Ambil nama lokasi dari Nominatim
      const locationName = await getLocationName(lat, lng);

      // Update marker
      if (marker) {
        marker.setLatLng(e.latlng);
        marker.bindPopup(locationName).openPopup();
      } else {
        const customIcon = L.icon({
          iconUrl:
            "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png",
          iconSize: [30, 45],
          iconAnchor: [15, 45],
          popupAnchor: [0, -45],
        });

        marker = L.marker(e.latlng, {
          icon: customIcon,
          draggable: true,
        }).addTo(map);

        marker.bindPopup(locationName).openPopup();

        // Event saat marker diseret
        marker.on("dragend", async (event) => {
          const pos = event.target.getLatLng();

          document.getElementById("lat").value = pos.lat.toFixed(6);
          document.getElementById("lng").value = pos.lng.toFixed(6);

          const name = await getLocationName(pos.lat, pos.lng);
          marker.setPopupContent(name).openPopup();

          const locationInfo = document.querySelector(".location-selected");
          if (locationInfo) {
            locationInfo.innerHTML = `<p>Lokasi dipilih: ${pos.lat.toFixed(
              6
            )}, ${pos.lng.toFixed(6)}</p>`;
          }
        });
      }
    });

    // Fungsi ambil nama lokasi
    async function getLocationName(lat, lng) {
      try {
        const res = await fetch(
          `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`
        );
        const data = await res.json();
        return data?.display_name || "Lokasi tidak ditemukan";
      } catch (err) {
        console.error("Gagal fetch nama lokasi:", err);
        return "Gagal mendapatkan nama lokasi";
      }
    }
  }

  fixLeafletDefaultIconPath() {
    // Get Leaflet's default icon
    const defaultIcon = L.Icon.Default.prototype.options;

    // Set the path to the icon images using absolute URLs
    defaultIcon.iconUrl =
      "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png";
    defaultIcon.iconRetinaUrl =
      "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png";
    defaultIcon.shadowUrl =
      "https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png";
  }

  // View interface methods for the presenter
  showLoading() {
    document.getElementById("submit-button").style.display = "none";
    document.getElementById("loading-indicator").style.display = "flex";
  }

  hideLoading() {
    document.getElementById("submit-button").style.display = "block";
    document.getElementById("loading-indicator").style.display = "none";
  }

  showSuccessMessage(message) {
    alert(message);
  }

  showErrorMessage(message) {
    alert(message);
  }

  // === FORM SUBMIT HANDLER ===
  setupFormSubmit() {
    const form = document.querySelector(".add-form");
    const cancelButton = document.getElementById("cancel-button");

    // Helper function to prepare story data
    const prepareStoryData = () => {
      // Stop camera stream if active
      if (this.stream) {
        this.stream.getTracks().forEach((track) => track.stop());
        this.stream = null;
        document.getElementById("camera-stream").srcObject = null;
      }

      const description = document.getElementById("description").value;
      const photoDataUrl = document.getElementById("photo-data").value;
      const lat = document.getElementById("lat").value;
      const lng = document.getElementById("lng").value;

      // Validate inputs
      if (!photoDataUrl) {
        alert("Please upload or take a photo.");
        return null;
      }

      if (!description) {
        alert("Please enter a description.");
        return null;
      }

      // Convert data URL to Blob
      const photoBlob = this.dataURLtoBlob(photoDataUrl);

      // Create story data
      const storyData = {
        description,
        photo: photoBlob,
      };

      // Add location if provided
      if (lat && lng) {
        storyData.lat = parseFloat(lat);
        storyData.lon = parseFloat(lng);
      }

      return storyData;
    };

    // Regular form submission (with authentication)
    form.addEventListener("submit", async (e) => {
      e.preventDefault();

      const storyData = prepareStoryData();
      if (!storyData) return;

      // Submit story using presenter
      const success = await this._presenter.submitStory(storyData);

      if (success) {
        // Redirect to home page
        window.location.hash = "#/home";
      }
    });

    // Cancel button click handler
    cancelButton.addEventListener("click", () => {
      // Confirm before canceling
      if (
        confirm(
          "Are you sure you want to cancel? All entered data will be lost."
        )
      ) {
        // Stop camera stream if active
        if (this.stream) {
          this.stream.getTracks().forEach((track) => track.stop());
          this.stream = null;
          document.getElementById("camera-stream").srcObject = null;
        }

        // Redirect to home page
        window.location.hash = "#/home";
      }
    });
  }

  // Helper function to convert data URL to Blob
  dataURLtoBlob(dataURL) {
    const parts = dataURL.split(";base64,");
    const contentType = parts[0].split(":")[1];
    const raw = window.atob(parts[1]);
    const rawLength = raw.length;
    const uInt8Array = new Uint8Array(rawLength);

    for (let i = 0; i < rawLength; ++i) {
      uInt8Array[i] = raw.charCodeAt(i);
    }

    return new Blob([uInt8Array], { type: contentType });
  }
}
