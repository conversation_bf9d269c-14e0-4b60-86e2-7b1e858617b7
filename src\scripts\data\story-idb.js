import { openDB } from "idb";

const DATABASE_NAME = "story-app-database";
const DATABASE_VERSION = 1;
const OBJECT_STORE_NAME = "stories";

// Initialize IndexedDB
const dbPromise = (() => {
  try {
    console.log("Initializing IndexedDB...");

    // Force close any existing connections first to avoid blocking
    const deleteRequest = indexedDB.deleteDatabase(DATABASE_NAME);

    deleteRequest.onsuccess = () => {
      console.log("Successfully reset IndexedDB");
    };

    deleteRequest.onerror = (event) => {
      console.error("Error resetting IndexedDB:", event);
    };

    // Create a new database with the correct schema
    return openDB(DATABASE_NAME, DATABASE_VERSION, {
      upgrade(database) {
        // Create object store if it doesn't exist
        if (!database.objectStoreNames.contains(OBJECT_STORE_NAME)) {
          database.createObjectStore(OBJECT_STORE_NAME, { keyPath: "id" });
          console.log(`Object store ${OBJECT_STORE_NAME} created`);
        }
      },
      blocked() {
        console.warn(
          "Database upgrade was blocked. Please close other tabs with this site open."
        );
      },
      blocking() {
        console.warn(
          "This page is blocking a database upgrade. Please reload."
        );
      },
      terminated() {
        console.error("Database connection was terminated unexpectedly.");
      },
    });
  } catch (error) {
    console.error("Error initializing IndexedDB:", error);
    // Return a promise that resolves to a mock DB for fallback
    return Promise.resolve({
      getAll: () => Promise.resolve([]),
      get: () => Promise.resolve(null),
      put: () => Promise.resolve(),
      delete: () => Promise.resolve(),
      clear: () => Promise.resolve(),
      transaction: () => ({
        objectStore: () => ({
          put: () => Promise.resolve(),
        }),
        done: Promise.resolve(),
      }),
    });
  }
})();

const StoryIdb = {
  /**
   * Get all stories from IndexedDB
   * @returns {Promise<Array>} Array of stories
   */
  async getAllStories() {
    try {
      const db = await dbPromise;
      return db.getAll(OBJECT_STORE_NAME);
    } catch (error) {
      console.error("Error getting all stories from IndexedDB:", error);
      return [];
    }
  },

  /**
   * Get a story by ID from IndexedDB
   * @param {string} id Story ID
   * @returns {Promise<Object>} Story object
   */
  async getStory(id) {
    if (!id) {
      return null;
    }
    try {
      const db = await dbPromise;
      return db.get(OBJECT_STORE_NAME, id);
    } catch (error) {
      console.error(`Error getting story ${id} from IndexedDB:`, error);
      return null;
    }
  },

  /**
   * Save stories to IndexedDB
   * @param {Array} stories Array of stories to save
   * @returns {Promise<void>}
   */
  async saveStories(stories) {
    if (!stories || !Array.isArray(stories)) {
      return;
    }

    try {
      const db = await dbPromise;
      const tx = db.transaction(OBJECT_STORE_NAME, "readwrite");
      const store = tx.objectStore(OBJECT_STORE_NAME);

      // Save each story
      await Promise.all(stories.map((story) => store.put(story)));

      // Wait for transaction to complete
      await tx.done;
      console.log("Stories saved to IndexedDB");
    } catch (error) {
      console.error("Error saving stories to IndexedDB:", error);
    }
  },

  /**
   * Save a single story to IndexedDB
   * @param {Object} story Story object to save
   * @returns {Promise<void>}
   */
  async saveStory(story) {
    if (!story || !story.id) {
      return;
    }
    try {
      const db = await dbPromise;
      await db.put(OBJECT_STORE_NAME, story);
      console.log(`Story ${story.id} saved to IndexedDB`);
    } catch (error) {
      console.error(`Error saving story ${story.id} to IndexedDB:`, error);
    }
  },

  /**
   * Delete a story from IndexedDB
   * @param {string} id Story ID to delete
   * @returns {Promise<void>}
   */
  async deleteStory(id) {
    if (!id) {
      return;
    }
    try {
      const db = await dbPromise;
      await db.delete(OBJECT_STORE_NAME, id);
      console.log(`Story ${id} deleted from IndexedDB`);
    } catch (error) {
      console.error(`Error deleting story ${id} from IndexedDB:`, error);
    }
  },

  /**
   * Clear all stories from IndexedDB
   * @returns {Promise<void>}
   */
  async clearStories() {
    try {
      const db = await dbPromise;
      await db.clear(OBJECT_STORE_NAME);
      console.log("All stories cleared from IndexedDB");
    } catch (error) {
      console.error("Error clearing stories from IndexedDB:", error);
    }
  },

  /**
   * Check if a story exists in IndexedDB
   * @param {string} id Story ID to check
   * @returns {Promise<boolean>} True if story exists, false otherwise
   */
  async hasStory(id) {
    if (!id) {
      return false;
    }
    const story = await this.getStory(id);
    return !!story;
  },
};

export default StoryIdb;
