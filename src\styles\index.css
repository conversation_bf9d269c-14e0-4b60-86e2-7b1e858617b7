/* === GENERAL === */
body {
  font-family: "Roboto", "Segoe UI", system-ui, sans-serif;
  background-color: #f0f4f8; /* Light blue-gray background */
  margin: 0;
  padding: 0;
  color: #2d3748;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.home-title {
  text-align: center;
  color: #2c5282; /* Dark blue */
  margin-bottom: 28px;
  position: relative;
  font-weight: 700;
}

/* === STORY LIST === */
.story-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.story-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

.story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 16px rgba(66, 153, 225, 0.15);
}

.story-img {
  width: 100%;
  height: 220px;
  object-fit: cover;
  border-bottom: 3px solid #4299e1;
}

.story-content {
  padding: 20px;
}

.story-name {
  font-size: 1.4em;
  margin: 0;
  color: #2c5282; /* Dark blue */
  font-weight: 600;
  border-bottom: 2px solid #bee3f8;
  padding-bottom: 8px;
  margin-bottom: 12px;
}

.story-description {
  color: #4a5568; /* Dark gray */
  margin: 12px 0;
  line-height: 1.6;
}

.story-date {
  color: #718096; /* Medium gray */
  font-size: 0.9em;
  font-style: italic;
}

/* === FORM === */
.add-form {
  background: #ffffff;
  max-width: 750px;
  margin: 0 auto;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  border: 1px solid #e2e8f0;
}

.add-form label {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.6rem;
  display: block;
  letter-spacing: 0.3px;
}

.add-form fieldset {
  border: 2px solid #bee3f8;
  border-radius: 12px;
  padding: 22px;
  margin-bottom: 1.8rem;
  background-color: #f8fafc;
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.add-form fieldset::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #4299e1, #3182ce, #2b6cb0);
}

.add-form fieldset:hover {
  border-color: #4299e1;
  box-shadow: 0 6px 16px rgba(66, 153, 225, 0.12);
  transform: translateY(-2px);
}

.add-form legend {
  font-weight: 700;
  color: #2c5282;
  padding: 0 16px;
  font-size: 1.2rem;
  background-color: #ebf8ff;
  border-radius: 20px;
  border: 1px solid #bee3f8;
  box-shadow: 0 2px 4px rgba(66, 153, 225, 0.1);
}

.form-group {
  margin-bottom: 1.5rem;
  width: 100%;
}

.form-hint {
  display: block;
  font-size: 0.85rem;
  color: #718096;
  margin-top: 0.6rem;
  font-style: italic;
  background-color: #ebf8ff;
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid #4299e1;
}

.required {
  color: #3182ce;
}

.add-form input,
.add-form textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #cbd5e0; /* Light gray */
  border-radius: 8px;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
  background-color: #f8fafc;
}

.add-form input:focus,
.add-form textarea:focus {
  border-color: #4299e1;
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
}

.add-form button {
  background-color: #4299e1; /* Blue */
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(66, 153, 225, 0.3);
}

.add-form button:hover {
  background-color: #3182ce; /* Darker blue */
  transform: translateY(-2px);
}

.add-form button:disabled {
  background-color: #cbd5e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.add-form button.secondary-button {
  background-color: #e2e8f0;
  color: #2d3748;
  border: 1px solid #cbd5e0;
  box-shadow: none;
}

.add-form button.secondary-button:hover {
  background-color: #cbd5e0;
  transform: translateY(-2px);
}

.add-form textarea {
  resize: vertical;
  min-height: 150px;
  width: 100%;
  max-width: 100%;
}

/* Photo input section */
.photo-input-section {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  margin-bottom: 1rem;
  position: relative;
}

/* Custom file input styling */
input[type="file"] {
  width: 100%;
  padding: 14px;
  border: 2px dashed #4299e1;
  border-radius: 10px;
  background-color: #ebf8ff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #2d3748;
  text-align: center;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

input[type="file"]::before {
  content: "📤";
  font-size: 1.5rem;
  margin-right: 8px;
  vertical-align: middle;
}

@keyframes pulse-border {
  0% {
    border-color: #4299e1;
  }
  50% {
    border-color: #3182ce;
  }
  100% {
    border-color: #4299e1;
  }
}

input[type="file"]:hover {
  background-color: #bee3f8;
  border-color: #3182ce;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.15);
  animation: pulse-border 1.5s infinite;
}

.or-divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 1.2rem 0;
}

.or-divider::before,
.or-divider::after {
  content: "";
  flex: 1;
  border-bottom: 2px dashed #4299e1;
}

.or-divider span {
  padding: 6px 20px;
  color: #2c5282;
  font-size: 1rem;
  font-weight: 600;
  background-color: #ebf8ff;
  border-radius: 20px;
  box-shadow: 0 2px 6px rgba(66, 153, 225, 0.15);
  border: 1px solid #bee3f8;
}

/* Photo preview */
.photo-preview {
  margin: 1.5rem 0;
  position: relative;
  border: 3px solid #4299e1;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(66, 153, 225, 0.15);
  transition: all 0.3s ease;
}

.photo-preview:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 24px rgba(66, 153, 225, 0.2);
}

.photo-preview img {
  width: 100%;
  max-height: 350px;
  object-fit: contain;
  display: block;
  background-color: #ebf8ff;
  padding: 10px;
}

.photo-preview button {
  position: absolute;
  top: 15px;
  right: 15px;
  background-color: rgba(229, 62, 62, 0.85);
  color: white;
  border: none;
  border-radius: 50%;
  width: 42px;
  height: 42px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25);
  z-index: 10;
}

.photo-preview button:hover {
  background-color: rgba(197, 48, 48, 0.95);
  transform: scale(1.15) rotate(90deg);
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.3);
}

/* === CAMERA === */
.camera-section {
  margin-top: 1.2rem;
  background-color: #ebf8ff;
  padding: 20px;
  border-radius: 12px;
  border: 2px solid #4299e1;
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.1);
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.camera-section::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(66, 153, 225, 0.1) 0%,
    rgba(66, 153, 225, 0) 70%
  );
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

.camera-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(66, 153, 225, 0.15);
}

.camera-section:hover::after {
  opacity: 1;
  animation: pulse-radial 2s infinite;
}

@keyframes pulse-radial {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1);
    opacity: 0.2;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
}

#camera-container {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.camera-controls {
  display: flex;
  gap: 1rem;
  margin-top: 0.8rem;
  flex-wrap: wrap;
}

#start-camera {
  background-color: #4299e1; /* Blue */
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: fit-content;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
  font-size: 1.1rem;
  margin: 0 auto;
  display: flex;
  align-items: center;
  gap: 8px;
}

#start-camera:hover {
  background-color: #3182ce; /* Darker blue */
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(66, 153, 225, 0.4);
}

#camera-stream {
  width: 100%;
  border: 3px solid #4299e1;
  border-radius: 12px;
  background-color: #ebf8ff;
  min-height: 280px;
  box-shadow: 0 8px 20px rgba(66, 153, 225, 0.15);
  transition: all 0.3s ease;
  margin: 0 auto;
}

#camera-stream:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 24px rgba(66, 153, 225, 0.2);
}

#snapshot {
  display: block;
  margin-top: 16px;
  max-width: 100%;
  border: 2px solid #bee3f8;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

#take-photo {
  background-color: #38a169; /* Green */
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(56, 161, 105, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
}

#take-photo:hover {
  background-color: #2f855a; /* Darker green */
  transform: translateY(-3px);
  box-shadow: 0 6px 14px rgba(56, 161, 105, 0.4);
}

#stop-camera {
  margin-top: 0.8rem;
  background-color: #e53e3e; /* Red */
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(229, 62, 62, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
}

#stop-camera:hover {
  background-color: #c53030; /* Darker red */
  transform: translateY(-3px);
  box-shadow: 0 6px 14px rgba(229, 62, 62, 0.4);
}

/* === MAP === */
.map-picker {
  height: 350px;
  border-radius: 10px;
  margin-bottom: 1.5rem;
  border: 2px solid #4299e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

input[type="hidden"] {
  display: none;
}

/* === Login Form === */
.login-container {
  max-width: 450px;
  margin: 100px auto;
  padding: 2.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  font-family: "Roboto", "Segoe UI", system-ui, sans-serif;
}

.login-container h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: #2c5282; /* Dark blue */
  font-weight: 700;
  font-size: 1.8rem;
  position: relative;
  padding-bottom: 12px;
}

.login-container h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background-color: #4299e1;
  border-radius: 2px;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.6rem;
  font-weight: 600;
  color: #2d3748;
  letter-spacing: 0.3px;
}

.form-group input {
  width: 100%;
  padding: 0.9rem;
  border: 1px solid #cbd5e0;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: #f8fafc;
}

.form-group input:focus {
  border-color: #4299e1;
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
}

button[type="submit"] {
  width: 100%;
  padding: 0.9rem;
  font-size: 1rem;
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(66, 153, 225, 0.3);
  letter-spacing: 0.5px;
}

button[type="submit"]:hover {
  background-color: #3182ce;
  transform: translateY(-2px);
}

.login-container p {
  text-align: center;
  margin-top: 1.5rem;
  color: #4a5568;
}

.login-container a {
  color: #3182ce;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.login-container a:hover {
  color: #2c5282;
  text-decoration: underline;
}

/* === Register Form === */
.register-container {
  max-width: 450px;
  margin: 100px auto;
  padding: 2.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  font-family: "Roboto", "Segoe UI", system-ui, sans-serif;
}

.register-container h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: #2c5282; /* Dark blue */
  font-weight: 700;
  font-size: 1.8rem;
  position: relative;
  padding-bottom: 12px;
}

.register-container h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background-color: #4299e1;
  border-radius: 2px;
}

.register-group {
  margin-bottom: 1.5rem;
}

.register-group label {
  display: block;
  margin-bottom: 0.6rem;
  font-weight: 600;
  color: #2d3748;
  letter-spacing: 0.3px;
}

/* Loading Indicator */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 1.5rem 0;
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(66, 153, 225, 0.2);
  border-radius: 50%;
  border-top-color: #4299e1;
  animation: spin 1.2s cubic-bezier(0.5, 0.1, 0.5, 0.9) infinite;
  margin-bottom: 0.8rem;
  box-shadow: 0 2px 10px rgba(66, 153, 225, 0.1);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-indicator p {
  color: #3182ce;
  font-size: 1rem;
  font-weight: 500;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 56px;
  height: 28px;
}

.toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #cbd5e0;
  transition: 0.4s ease;
  border-radius: 28px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-label:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s ease;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.toggle-input:checked + .toggle-label {
  background-color: #4299e1;
}

.toggle-input:checked + .toggle-label:before {
  transform: translateX(28px);
}

/* Pagination Controls */
.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2.5rem;
  gap: 1.2rem;
}

.pagination-btn {
  padding: 0.7rem 1.2rem;
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(66, 153, 225, 0.3);
}

.pagination-btn:hover:not(:disabled) {
  background-color: #3182ce;
  transform: translateY(-2px);
}

.pagination-btn:disabled {
  background-color: #cbd5e0;
  cursor: not-allowed;
  box-shadow: none;
}

#page-info {
  font-weight: 600;
  color: #2d3748;
  font-size: 1.1rem;
}

/* Story Location */
.story-location {
  margin-top: 0.8rem;
  color: #4a5568;
  background-color: #ebf8ff;
  padding: 6px 10px;
  border-radius: 6px;
  display: inline-block;
  font-size: 0.9rem;
  border-left: 3px solid #4299e1;
}

/* Map Section */
.map-section {
  margin: 2.5rem 0;
  padding: 1.5rem;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.section-title {
  margin-bottom: 1.5rem;
  color: #2c5282;
  text-align: center;
  font-size: 1.5rem;
  font-weight: 700;
  position: relative;
  padding-bottom: 10px;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: #4299e1;
  border-radius: 2px;
}

.map-container {
  height: 450px;
  width: 100%;
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid #4299e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* Leaflet Popup Customization */
.leaflet-popup-content {
  margin: 12px;
}

.marker-popup {
  max-width: 280px;
}

.marker-popup h4 {
  margin: 0 0 10px 0;
  color: #2c5282;
  font-weight: 600;
  border-bottom: 2px solid #bee3f8;
  padding-bottom: 6px;
}

.marker-popup img {
  border-radius: 8px;
  margin-bottom: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.marker-popup p {
  margin: 10px 0;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #4a5568;
}

.marker-popup small {
  color: #718096;
  font-size: 0.85rem;
  font-style: italic;
}

/* Maps Page */
.maps-title {
  text-align: center;
  color: #2c5282;
  margin-bottom: 28px;
  font-size: 2rem;
  font-weight: 700;
  position: relative;
  padding-bottom: 12px;
}

.maps-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: #4299e1;
  border-radius: 2px;
}

.map-controls {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.current-location-btn {
  background-color: #000;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.current-location-btn:hover {
  background-color: #111;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.25);
}

.current-location-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.map-container-fullscreen {
  height: 75vh;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  border: 2px solid #4299e1;
  position: relative;
}

.map-instruction {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  border: 1px solid #4299e1;
}

.map-instruction p {
  margin: 0;
  font-weight: 600;
  color: #2c5282;
  font-size: 0.9rem;
}

.location-selected {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  border: 1px solid #4299e1;
  max-width: 80%;
}

.location-selected p {
  margin: 0;
  font-size: 0.85rem;
  color: #2d3748;
}

.register-group input {
  width: 100%;
  padding: 0.9rem;
  border: 1px solid #cbd5e0;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: #f8fafc;
}

.register-group input:focus {
  border-color: #4299e1;
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
}

button[type="submit"].register-btn {
  width: 100%;
  padding: 0.9rem;
  font-size: 1rem;
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(66, 153, 225, 0.3);
  letter-spacing: 0.5px;
}

button[type="submit"].register-btn:hover {
  background-color: #3182ce;
  transform: translateY(-2px);
}

.register-container p {
  text-align: center;
  margin-top: 1.5rem;
  color: #4a5568;
}

.register-container a {
  color: #3182ce;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.register-container a:hover {
  color: #2c5282;
  text-decoration: underline;
}
