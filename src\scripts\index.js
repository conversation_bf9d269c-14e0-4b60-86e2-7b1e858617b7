// CSS imports
import "../styles/index.css";
import "../styles/styles.css";

import App from "./pages/app";
import NotificationManager from "./utils/notification-manager";

// Function to register service worker
const registerServiceWorker = async () => {
  if ("serviceWorker" in navigator) {
    try {
      await navigator.serviceWorker.register("/sw.js");
      console.log("Service worker registered successfully");
    } catch (error) {
      console.error("Service worker registration failed:", error);
    }
  }
};

// Add a default login for testing
const addDefaultLogin = () => {
  const AuthService = {
    setAuth(userData) {
      localStorage.setItem("story_app_auth", JSON.stringify(userData));
    },
  };

  // Check if already logged in
  if (!localStorage.getItem("story_app_auth")) {
    // Add a default login for testing
    AuthService.setAuth({
      id: "test-user-id",
      name: "Test User",
      token: "test-token",
      email: "<EMAIL>",
    });
    console.log("Added default login for testing");
  }
};

// Function to handle notification dialog
const handleNotificationDialog = () => {
  const dialog = document.getElementById("notification-dialog");
  const allowButton = document.getElementById("notification-allow");
  const denyButton = document.getElementById("notification-deny");

  // Check if notification permission has already been granted or denied
  if (
    Notification.permission === "granted" ||
    Notification.permission === "denied" ||
    localStorage.getItem("notification_permission_asked")
  ) {
    return;
  }

  // Show the dialog
  setTimeout(() => {
    dialog.style.display = "block";
  }, 3000);

  // Handle allow button click
  allowButton.addEventListener("click", async () => {
    dialog.style.display = "none";
    localStorage.setItem("notification_permission_asked", "true");

    try {
      if (window.notificationManager) {
        await window.notificationManager.subscribe();
      } else {
        await Notification.requestPermission();
      }
    } catch (error) {
      console.error("Error requesting notification permission:", error);
    }
  });

  // Handle deny button click
  denyButton.addEventListener("click", () => {
    dialog.style.display = "none";
    localStorage.setItem("notification_permission_asked", "true");
  });
};

// Function to set up notification toggle button in the navigation
const setupNotificationToggleButton = async (notificationManager) => {
  const toggleButton = document.getElementById("notification-toggle-nav");
  if (!toggleButton || !notificationManager) return;

  try {
    // Check if notifications are supported
    const isSupported = notificationManager.isNotificationSupported();
    if (!isSupported) {
      toggleButton.style.display = "none";
      return;
    }

    // Check current subscription status
    const isSubscribed = await notificationManager.isSubscribed();

    // Update button appearance based on subscription status
    updateNotificationButtonState(toggleButton, isSubscribed);

    // Add click event listener
    toggleButton.addEventListener("click", async () => {
      try {
        // Disable button during processing
        toggleButton.disabled = true;
        toggleButton.style.opacity = "0.7";
        toggleButton.style.cursor = "wait";

        // Get current subscription status directly from the button's class
        const isCurrentlySubscribed = toggleButton.classList.contains("active");
        console.log("Current subscription status:", isCurrentlySubscribed);

        if (isCurrentlySubscribed) {
          // Unsubscribe
          console.log("Attempting to unsubscribe...");
          const success = await notificationManager.unsubscribe();
          console.log("Unsubscribe result:", success);

          if (success) {
            // Update button state immediately
            toggleButton.classList.remove("active");
            console.log("Button state updated to inactive");
            alert("Successfully unsubscribed from notifications!");
          } else {
            alert(
              "Failed to unsubscribe from notifications. Please try again."
            );
          }
        } else {
          // Subscribe
          console.log("Attempting to subscribe...");
          const success = await notificationManager.subscribe();
          console.log("Subscribe result:", success);

          if (success) {
            // Update button state immediately
            toggleButton.classList.add("active");
            console.log("Button state updated to active");
            alert("Successfully subscribed to notifications!");
          } else {
            alert("Failed to subscribe to notifications. Please try again.");
          }
        }
      } catch (error) {
        console.error("Error toggling notification subscription:", error);
        alert("An error occurred while managing notifications.");
      } finally {
        // Re-enable button
        toggleButton.disabled = false;
        toggleButton.style.opacity = "1";
        toggleButton.style.cursor = "pointer";
      }
    });
  } catch (error) {
    console.error("Error setting up notification toggle button:", error);
    toggleButton.style.display = "none";
  }
};

// Helper function to update notification button state
const updateNotificationButtonState = (button, isSubscribed) => {
  if (isSubscribed) {
    button.classList.add("active");
    button.setAttribute("aria-label", "Disable notifications");
  } else {
    button.classList.remove("active");
    button.setAttribute("aria-label", "Enable notifications");
  }
  // Always show the bell icon
  button.textContent = "🔔";
};

document.addEventListener("DOMContentLoaded", async () => {
  // Define app variable in a wider scope
  let app;

  try {
    console.log("DOM content loaded, initializing app...");

    // Add default login for testing
    addDefaultLogin();

    // Initialize app first to ensure UI is responsive
    app = new App({
      content: document.querySelector("#main-content"),
      drawerButton: document.querySelector("#drawer-button"),
      navigationDrawer: document.querySelector("#navigation-drawer"),
    });

    // Render initial page immediately
    await app.renderPage();
    console.log("Initial page rendered");

    // Register service worker in the background
    registerServiceWorker().catch((error) => {
      console.error("Background service worker registration failed:", error);
      // Continue app execution even if service worker fails
    });

    // Initialize notification manager in the background
    setTimeout(async () => {
      try {
        console.log("Initializing notification manager...");
        const notificationManager = new NotificationManager();
        const result = await notificationManager.init();

        if (result) {
          console.log("Notification manager initialized successfully");
          // Show notification dialog after notification manager is initialized
          handleNotificationDialog();

          // Set up notification toggle button in the navigation
          setupNotificationToggleButton(notificationManager);
        } else {
          console.log(
            "Notification manager initialized with limited functionality"
          );
        }

        // Store notification manager in window for global access
        window.notificationManager = notificationManager;
      } catch (error) {
        console.error("Error initializing notification manager:", error);
        // Create a dummy notification manager to prevent errors
        window.notificationManager = {
          isNotificationSupported: () => false,
          isSubscribed: () => Promise.resolve(false),
          subscribe: () => Promise.resolve(false),
          unsubscribe: () => Promise.resolve(false),
        };
      }
    }, 1000); // Delay notification initialization to ensure UI is responsive
  } catch (error) {
    console.error("Error in main initialization:", error);

    // Try to render a basic app if initialization fails
    if (!app) {
      try {
        app = new App({
          content: document.querySelector("#main-content"),
          drawerButton: document.querySelector("#drawer-button"),
          navigationDrawer: document.querySelector("#navigation-drawer"),
        });
        await app.renderPage();
        console.log("Fallback page rendering successful");
      } catch (fallbackError) {
        console.error("Fallback rendering failed:", fallbackError);
        // Display a basic error message to the user
        document.querySelector("#main-content").innerHTML = `
          <div style="text-align: center; margin-top: 2rem;">
            <h2>Something went wrong</h2>
            <p>Please try refreshing the page.</p>
          </div>
        `;
      }
    }
  }

  // Set up hash change event listener
  window.addEventListener("hashchange", async () => {
    if (app) {
      try {
        await app.renderPage();
      } catch (error) {
        console.error("Error rendering page on hash change:", error);
      }
    }
  });

  // Add event listener for page unload to ensure camera is stopped
  window.addEventListener("beforeunload", () => {
    // Get the current route
    const currentHash = window.location.hash.replace("#", "") || "/";

    // If we're on the add page, clean up the camera
    if (currentHash === "/add" && app) {
      try {
        const routes = app.getRoutes();
        if (
          routes &&
          routes["/add"] &&
          typeof routes["/add"].cleanup === "function"
        ) {
          routes["/add"].cleanup();
        }
      } catch (error) {
        console.error("Error cleaning up camera:", error);
      }
    }
  });
});
