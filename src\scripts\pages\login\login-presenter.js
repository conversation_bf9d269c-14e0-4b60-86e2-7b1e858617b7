import AuthService from '../../utils/auth';
import StoryAPI from '../../data/api';

class LoginPresenter {
  constructor({ view, authService, storyAPI }) {
    this._view = view;
    this._authService = authService || AuthService;
    this._storyAPI = storyAPI || StoryAPI;
  }

  async login(email, password) {
    try {
      // Basic validation
      if (!email || !password) {
        this._view.showErrorMessage('Please fill in both fields');
        return false;
      }

      this._view.showLoading();

      // Call the login API
      const response = await this._storyAPI.login({ email, password });

      this._view.hideLoading();

      if (response.error === false) {
        // Login successful
        const { loginResult } = response;

        // Store authentication data
        this._authService.setAuth({
          id: loginResult.userId,
          name: loginResult.name,
          token: loginResult.token,
          email: email,
        });

        // Show success message
        this._view.showSuccessMessage('Login successful!');
        return true;
      } else {
        // Handle login failure
        this._view.showErrorMessage(response.message || '<PERSON>gin failed. Please check your credentials.');
        return false;
      }
    } catch (error) {
      console.error('Error during login:', error);
      this._view.hideLoading();
      this._view.showErrorMessage('Login failed. Please try again later.');
      return false;
    }
  }
}

export default LoginPresenter;
