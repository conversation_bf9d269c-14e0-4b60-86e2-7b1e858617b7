import LogoutPresenter from './logout-presenter';

export default class LogoutPage {
  constructor() {
    this._presenter = new LogoutPresenter({
      view: this,
    });
  }

  async render() {
    return `
      <div class="logout-container">
        <h2>Logging out...</h2>
      </div>
    `;
  }

  async afterRender() {
    // Use presenter to handle logout
    this._presenter.logout();

    // Redirect to login page after a short delay
    setTimeout(() => {
      window.location.hash = '#/login';
    }, 1000);
  }

  // View interface methods for the presenter
  showLogoutMessage() {
    // Optional: You could update the UI to show a message
    console.log('Logged out successfully');
  }
}
