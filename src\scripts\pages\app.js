import routes from "../routes/routes";
import { getActivePathname, getActiveRoute } from "../routes/url-parser";
import AuthService from "../utils/auth";

class App {
  #content = null;
  #drawerButton = null;
  #navigationDrawer = null;
  #currentPage = null;

  constructor({ navigationDrawer, drawerButton, content }) {
    this.#content = content;
    this.#drawerButton = drawerButton;
    this.#navigationDrawer = navigationDrawer;

    this._setupDrawer();
  }

  // Method to get routes for external access
  getRoutes() {
    return routes;
  }

  _setupDrawer() {
    // Setup drawer toggle
    this.#drawerButton.addEventListener("click", (event) => {
      this.#navigationDrawer.classList.toggle("open");
      // Update ARIA attributes
      const isExpanded = this.#navigationDrawer.classList.contains("open");
      this.#drawerButton.setAttribute("aria-expanded", isExpanded);
      event.stopPropagation();
    });

    document.body.addEventListener("click", (event) => {
      if (
        !this.#navigationDrawer.contains(event.target) &&
        !this.#drawerButton.contains(event.target)
      ) {
        this.#navigationDrawer.classList.remove("open");
        this.#drawerButton.setAttribute("aria-expanded", "false");
      }

      this.#navigationDrawer.querySelectorAll("a").forEach((link) => {
        if (link.contains(event.target)) {
          this.#navigationDrawer.classList.remove("open");
          this.#drawerButton.setAttribute("aria-expanded", "false");
        }
      });
    });

    // Handle keyboard navigation for accessibility
    this.#navigationDrawer.addEventListener("keydown", (event) => {
      if (event.key === "Escape") {
        this.#navigationDrawer.classList.remove("open");
        this.#drawerButton.setAttribute("aria-expanded", "false");
        this.#drawerButton.focus();
      }
    });
  }

  async renderPage() {
    try {
      console.log("Rendering page...");
      const url = getActiveRoute();
      const pathname = getActivePathname();

      console.log("Current URL:", url);
      console.log("Current pathname:", pathname);
      console.log("Available routes:", Object.keys(routes));

      // Remove the initial content if it exists
      const initialContent = document.getElementById("initial-content");
      if (initialContent) {
        initialContent.remove();
      }

      // Check if the route exists
      if (!routes[url]) {
        console.log(`Route "${url}" not found, rendering not-found content directly`);
        // Render not found content directly instead of redirecting
        this._renderNotFoundPage();
        return;
      }

      // Check if user is authenticated
      const isAuthenticated = AuthService.isAuthenticated();
      console.log("Is authenticated:", isAuthenticated);

      // Define public routes that don't require authentication
      const publicRoutes = ["/login", "/register"];

      // If user is not authenticated and trying to access a protected route
      if (!isAuthenticated && !publicRoutes.includes(pathname)) {
        console.log("Not authenticated, redirecting to login");
        // Redirect to login page
        window.location.hash = "#/login";
        return;
      }

      // If user is authenticated and trying to access login/register page
      if (isAuthenticated && publicRoutes.includes(pathname)) {
        console.log("Already authenticated, redirecting to home");
        // Redirect to home page
        window.location.hash = "#/home";
        return;
      }
    } catch (error) {
      console.error("Error in renderPage routing:", error);
      return;
    }

    // Get the current URL again to ensure it's in scope
    const url = getActiveRoute();

    // Cleanup previous page if it's the Add page and we're navigating away from it
    if (this.#currentPage === "/add" && url !== "/add") {
      // Get the Add page instance and call cleanup
      const addPage = routes["/add"];
      if (addPage && typeof addPage.cleanup === "function") {
        addPage.cleanup();
      }
    }

    // Use View Transition API if supported and page has changed
    if (document.startViewTransition && this.#currentPage !== url) {
      try {
        await document.startViewTransition(async () => {
          // Render the page
          const page = routes[url];
          this.#content.innerHTML = await page.render();
          await page.afterRender();

          // Update current page
          this.#currentPage = url;

          // Set focus to main content for accessibility
          this.#content.focus();
        }).finished;
      } catch (error) {
        console.error("View Transition failed:", error);
        this._fallbackRender(url);
      }
    } else {
      // Fallback for browsers that don't support View Transition API
      await this._fallbackRender(url);
    }
  }

  async _fallbackRender(url) {
    try {
      // Cleanup previous page if it's the Add page and we're navigating away from it
      if (this.#currentPage === "/add" && url !== "/add") {
        try {
          // Get the Add page instance and call cleanup
          const addPage = routes["/add"];
          if (addPage && typeof addPage.cleanup === "function") {
            addPage.cleanup();
          }
        } catch (cleanupError) {
          console.error("Error cleaning up previous page:", cleanupError);
          // Continue with rendering even if cleanup fails
        }
      }

      // Render the page
      const page = routes[url];
      if (!page) {
        console.error(`Page for route "${url}" not found`);
        // Render not found content directly
        this._renderNotFoundPage();
        return;
      }

      // Render the page content
      try {
        this.#content.innerHTML = await page.render();
      } catch (renderError) {
        console.error("Error rendering page:", renderError);
        this.#content.innerHTML = `
          <div style="text-align: center; margin-top: 2rem;">
            <h2>Error Rendering Page</h2>
            <p>Something went wrong while loading this page.</p>
            <a href="#/home" class="back-button">Go to Home</a>
          </div>
        `;
        return;
      }

      // Call afterRender
      try {
        await page.afterRender();
      } catch (afterRenderError) {
        console.error("Error in afterRender:", afterRenderError);
        // Continue even if afterRender fails
      }

      // Update current page
      this.#currentPage = url;

      // Set focus to main content for accessibility
      this.#content.focus();
    } catch (error) {
      console.error("Fatal error in fallback render:", error);
      // Last resort error handling
      this.#content.innerHTML = `
        <div style="text-align: center; margin-top: 2rem;">
          <h2>Something Went Wrong</h2>
          <p>Please try refreshing the page.</p>
        </div>
      `;
    }
  }

  _renderNotFoundPage() {
    // Render an attractive 404 page directly
    this.#content.innerHTML = `
      <section class="not-found-container">
        <div class="not-found-content">
          <h2 class="not-found-title">404 - Page Not Found</h2>
          <div class="not-found-image">
            <svg width="280" height="210" viewBox="0 0 280 210" xmlns="http://www.w3.org/2000/svg" class="not-found-svg">
              <!-- Background -->
              <rect width="280" height="210" fill="#f8f9fa" rx="10" ry="10"/>

              <!-- 404 Text -->
              <text x="140" y="70" font-family="Arial, sans-serif" font-size="42" font-weight="bold" text-anchor="middle" fill="#4299e1">404</text>

              <!-- Broken page icon -->
              <g transform="translate(105, 105)">
                <!-- Page outline -->
                <rect x="0" y="0" width="70" height="84" fill="white" stroke="#4299e1" stroke-width="3" rx="5" ry="5"/>

                <!-- Torn corner -->
                <path d="M70,14 L56,0 L56,14 Z" fill="#e2e8f0" stroke="#4299e1" stroke-width="1.5"/>

                <!-- Page lines -->
                <line x1="14" y1="21" x2="56" y2="21" stroke="#a0aec0" stroke-width="1.5"/>
                <line x1="14" y1="35" x2="56" y2="35" stroke="#a0aec0" stroke-width="1.5"/>
                <line x1="14" y1="49" x2="56" y2="49" stroke="#a0aec0" stroke-width="1.5"/>
                <line x1="14" y1="63" x2="56" y2="63" stroke="#a0aec0" stroke-width="1.5"/>

                <!-- Sad face -->
                <circle cx="35" cy="42" r="17.5" fill="none" stroke="#e53e3e" stroke-width="1.5"/>
                <circle cx="28" cy="35" r="2.1" fill="#e53e3e"/>
                <circle cx="42" cy="35" r="2.1" fill="#e53e3e"/>
                <path d="M24.5,52.5 Q35,45.5 45.5,52.5" fill="none" stroke="#e53e3e" stroke-width="1.5"/>
              </g>

              <!-- Message -->
              <text x="140" y="175" font-family="Arial, sans-serif" font-size="11.2" text-anchor="middle" fill="#2d3748">The page you're looking for doesn't exist</text>
            </svg>
          </div>
          <p class="not-found-message">
            Oops! The page you're looking for doesn't exist or has been moved.
          </p>
          <div class="not-found-actions">
            <a href="#/home" class="not-found-button primary-button">Go to Home</a>
            <button onclick="window.history.back()" class="not-found-button secondary-button">
              Go Back
            </button>
          </div>
        </div>
      </section>
    `;

    // Add event listener for the back button
    setTimeout(() => {
      const backButton = document.querySelector('.secondary-button');
      if (backButton) {
        backButton.addEventListener('click', (event) => {
          event.preventDefault();
          window.history.back();
        });
      }
    }, 100);
  }
}

export default App;
