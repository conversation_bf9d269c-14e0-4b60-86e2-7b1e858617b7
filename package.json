{"name": "app-starter-project-with-webpack", "version": "1.0.0", "description": "", "scripts": {"build": "webpack --config webpack.prod.js", "start-dev": "webpack serve --config webpack.dev.js", "serve": "http-server dist"}, "keywords": [], "author": "YOUR_NAME <<EMAIL>>", "license": "ISC", "devDependencies": {"@babel/preset-env": "^7.26.9", "babel-loader": "^10.0.0", "canvas": "^3.1.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^13.0.0", "css-loader": "^7.1.2", "html-webpack-plugin": "^5.6.3", "http-server": "^14.1.1", "mini-css-extract-plugin": "^2.9.2", "style-loader": "^4.0.0", "webpack": "^5.98.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0", "webpack-merge": "^6.0.1"}, "dependencies": {"idb": "^8.0.3", "leaflet": "^1.9.4", "leaflet-providers": "^2.0.0", "leaflet.vectorgrid": "^1.3.0", "regenerator-runtime": "^0.14.1", "workbox-core": "^7.3.0", "workbox-expiration": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0", "workbox-webpack-plugin": "^7.3.0", "workbox-window": "^7.3.0"}}