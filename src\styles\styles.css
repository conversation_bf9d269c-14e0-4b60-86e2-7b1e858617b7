* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Roboto", "Segoe UI", system-ui, sans-serif;
  background-color: #f0f4f8;
  color: #2d3748;
  line-height: 1.7;
}

/* Accessibility: Skip to content link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background-color: #3182ce;
  color: white;
  padding: 10px 15px;
  z-index: 10000;
  transition: top 0.4s ease;
  border-radius: 0 0 8px 0;
  text-decoration: none;
  font-weight: 500;
}

.skip-link:focus {
  top: 0;
}

/* Focus styles for better accessibility */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 3px solid #4299e1;
  outline-offset: 3px;
}

/* Visually hidden elements (for screen readers) */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  white-space: nowrap;
}

/* View Transitions */
@keyframes slide-in {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-out {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-20px);
    opacity: 0;
  }
}

::view-transition-old(root) {
  animation: 350ms cubic-bezier(0.3, 0, 0.3, 1) both slide-out;
}

::view-transition-new(root) {
  animation: 450ms cubic-bezier(0.2, 0, 0.2, 1) both slide-in;
}

.container {
  padding-inline: 24px;
  margin-inline: auto;
  max-width: 1200px;
}

header {
  background-color: #2c5282; /* Dark blue background */
  border-bottom: 3px solid #4299e1;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.main-header {
  min-height: 80px;
  padding-block: 16px;

  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.brand-name {
  font-size: 1.3rem;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-decoration: none;
  color: #ebf8ff; /* Light blue text */
  text-transform: uppercase;
  display: flex;
  align-items: center;
  gap: 8px;
}

.navigation-drawer {
  min-height: 100vh;
  width: 240px;
  padding: 24px 16px;
  background-color: #2d3748;

  position: fixed;
  inset-block-start: 0;
  inset-inline-start: 0;

  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.2);
  transform: translateX(-100%);
  transition: transform 400ms cubic-bezier(0.4, 0, 0.2, 1);

  z-index: 9999;
}

.navigation-drawer.open {
  transform: translateX(0);
}

.drawer-button {
  padding: 10px;
  border: 0;
  border-radius: 6px;

  display: inline-block;
  background-color: #4299e1; /* Blue button */
  font-weight: bold;
  font-size: 24px;
  color: white;

  cursor: pointer;
  transition: background-color 0.3s;
}

.drawer-button:hover {
  background-color: #3182ce;
}

.nav-list {
  list-style: none;

  display: flex;
  flex-direction: column;
  gap: 12px;
}

.nav-list li a {
  padding: 10px 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e2e8f0; /* Light gray text */
  text-decoration: none;
  border-radius: 6px;
  transition: all 250ms ease;
  font-weight: 500;
}

.nav-list li a:hover {
  background-color: #4299e1; /* Blue hover */
  color: white;
  transform: translateX(5px);
}

/* Navigation item with button */
.nav-item-with-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-button {
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 10px;
  position: relative;
  outline: none;
}

.notification-button:hover {
  background-color: #3182ce;
  transform: scale(1.1);
}

.notification-button:disabled {
  opacity: 0.7;
  cursor: wait;
  transform: none;
}

.notification-button.active {
  background-color: #4299e1;
}

.notification-button.active:hover:not(:disabled) {
  background-color: #3182ce;
}

/* Slash for inactive notification */
.notification-button:not(.active)::after {
  content: "";
  position: absolute;
  width: 3px;
  height: 24px;
  background-color: #e53e3e;
  transform: rotate(45deg);
  border-radius: 1px;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

/* MAIN */
.main-content {
  padding-block: 32px;
  min-height: calc(100vh - 180px); /* Adjust for header and footer */
  background-color: #f0f4f8;
}

/* Footer Styles */
.footer {
  background-color: #2c5282;
  padding: 24px 0;
  text-align: center;
  border-top: 3px solid #4299e1;
  margin-top: 30px;
  color: #e2e8f0;
  font-weight: 500;
}

/* Map Styles */
.map-container {
  height: 320px;
  width: 100%;
  margin-bottom: 24px;
  border-radius: 12px;
  border: 2px solid #4299e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.map-container-fullscreen {
  height: 520px;
  width: 100%;
  margin-bottom: 24px;
  border-radius: 12px;
  border: 2px solid #4299e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.map-section {
  margin-bottom: 32px;
  background-color: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.section-title {
  margin-bottom: 16px;
  color: #2c5282;
  font-size: 1.4rem;
  font-weight: 600;
  border-bottom: 2px solid #bee3f8;
  padding-bottom: 8px;
}

.marker-popup h4 {
  color: #2c5282;
  margin-bottom: 10px;
  font-weight: 600;
}

.marker-popup img {
  border-radius: 8px;
  margin-bottom: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.marker-popup p {
  margin-bottom: 10px;
  line-height: 1.5;
}

/* Layer Control Styles */
.leaflet-control-layers {
  border-radius: 10px !important;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #4299e1 !important;
  background-color: #ebf8ff !important;
}

.leaflet-control-layers-toggle {
  background-color: #4299e1 !important;
  border-radius: 6px !important;
}

.leaflet-control-layers-expanded {
  padding: 12px !important;
  color: #2c5282 !important;
  font-weight: 600 !important;
}

.leaflet-control-layers-selector {
  margin-right: 8px !important;
}

/* Filter Controls */
.filter-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 18px;
  margin-bottom: 24px;
  padding: 18px;
  background-color: white;
  border-radius: 12px;
  border: 1px solid #bee3f8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.size-select {
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #bee3f8;
  background-color: #ebf8ff;
  color: #2c5282;
  font-weight: 500;
}

/* Pagination Controls */
.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 18px;
  margin-top: 30px;
  margin-bottom: 30px;
}

.pagination-btn {
  padding: 10px 18px;
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.pagination-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #3182ce;
  transform: translateY(-2px);
}

#page-info {
  font-weight: 600;
  color: #2c5282;
  font-size: 1.1rem;
}

/* Detail Page Styles */
.detail-container {
  margin-bottom: 32px;
}

.detail-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.detail-img {
  width: 100%;
  max-height: 450px;
  object-fit: cover;
  border-bottom: 3px solid #4299e1;
}

.detail-content {
  padding: 28px;
}

.detail-name {
  font-size: 1.8rem;
  color: #2c5282;
  margin-bottom: 16px;
  font-weight: 700;
  border-bottom: 2px solid #bee3f8;
  padding-bottom: 8px;
}

.detail-description {
  margin-bottom: 20px;
  line-height: 1.8;
  color: #4a5568;
}

.detail-date {
  display: block;
  color: #718096;
  margin-bottom: 16px;
  font-style: italic;
}

.detail-location {
  margin-top: 16px;
  padding: 8px 14px;
  background-color: #ebf8ff;
  border-radius: 8px;
  display: inline-block;
  font-size: 0.95rem;
  border-left: 4px solid #4299e1;
}

.location-dot {
  color: #3182ce;
  font-size: 1.3rem;
  vertical-align: middle;
  margin-right: 6px;
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #4299e1;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(66, 153, 225, 0.3);
}

.back-button:hover {
  background-color: #3182ce;
  transform: translateY(-2px);
}

/* Notification Settings Styles */
.notification-settings {
  margin: 20px 0;
  padding: 15px;
  background-color: #ebf8ff;
  border-radius: 8px;
  border: 1px solid #bee3f8;
}

.notification-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle-label {
  font-weight: 600;
  color: #2c5282;
}

.toggle-button {
  padding: 8px 16px;
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.toggle-button:hover {
  background-color: #3182ce;
}

.toggle-button.active {
  background-color: #e53e3e;
}

.toggle-button.active:hover {
  background-color: #c53030;
}

.notification-not-supported {
  color: #718096;
  font-style: italic;
}

/* Not Found Page Styles */
.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 70vh;
  padding: 2rem;
}

.not-found-content {
  max-width: 600px;
  text-align: center;
  background-color: white;
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.not-found-title {
  font-size: 2.5rem;
  color: #2c5282;
  margin-bottom: 1.5rem;
  font-weight: 700;
}

.not-found-image {
  margin: 1.5rem auto;
  max-width: 300px;
}

.not-found-image img {
  width: 100%;
  height: auto;
}

.not-found-message {
  font-size: 1.2rem;
  color: #4a5568;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.not-found-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.not-found-button {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.primary-button {
  background-color: #4299e1;
  color: white;
  border: none;
}

.primary-button:hover {
  background-color: #3182ce;
  transform: translateY(-2px);
}

.secondary-button {
  background-color: #e2e8f0;
  color: #2d3748;
  border: none;
}

.secondary-button:hover {
  background-color: #cbd5e0;
  transform: translateY(-2px);
}

/* Favorite Button Styles */
.favorite-button {
  padding: 10px 16px;
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-top: 16px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.favorite-button:hover {
  background-color: #3182ce;
  transform: translateY(-2px);
}

.favorite-button.favorite {
  background-color: #e53e3e;
}

.favorite-button.favorite:hover {
  background-color: #c53030;
}

/* Story Link Styles */
.story-link {
  text-decoration: none;
  color: inherit;
  display: block;
  transition: transform 0.3s ease;
}

.view-detail {
  margin-top: 14px;
  text-align: right;
}

.view-detail span {
  display: inline-flex;
  align-items: center;
  padding: 8px 14px;
  background-color: #4299e1;
  color: white;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.story-link:hover .view-detail span {
  background-color: #3182ce;
  padding-right: 18px;
}

.story-link:hover .view-detail span::after {
  content: "→";
  margin-left: 6px;
}

/* Page Titles */
.home-title,
.detail-title,
.maps-title {
  text-align: center;
  color: #2c5282;
  margin-bottom: 2rem;
  font-size: 2rem;
  font-weight: 700;
  position: relative;
  padding-bottom: 12px;
}

.home-title::after,
.detail-title::after,
.maps-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: #4299e1;
  border-radius: 2px;
}

@media screen and (width > 1000px) {
  .navigation-drawer {
    min-height: auto;
    width: auto;
    padding: 0;
    background-color: transparent;

    flex-direction: row;
    justify-content: space-between;

    position: static;
    box-shadow: none;
    transform: translateX(0);
  }

  .nav-list {
    flex-direction: row;
  }

  .nav-list li a {
    display: inline-block;
    color: #e2e8f0;
    padding: 8px 16px;
    margin: 0 2px;
    border-radius: 6px;
  }

  .nav-list li a:hover {
    background-color: rgba(66, 153, 225, 0.7);
    transform: translateY(-2px);
  }

  .drawer-button {
    display: none;
  }
}

/* Responsive FAB for mobile devices */
@media screen and (max-width: 768px) {
  .add-story-fab {
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
  }

  .add-story-fab i {
    font-size: 20px;
  }
}

/* Add New Story Button Styles */
.button-group {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.primary-button,
.guest-button {
  flex: 1;
  padding: 14px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.primary-button {
  background-color: #4299e1;
  color: white;
}

.primary-button:hover {
  background-color: #3182ce;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(66, 153, 225, 0.3);
}

.guest-button {
  background-color: #e2e8f0;
  color: #2d3748;
  border: 1px solid #cbd5e0;
}

.guest-button:hover {
  background-color: #cbd5e0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Floating Action Button (FAB) */
.add-story-fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  background-color: #4299e1; /* Blue to match theme */
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(66, 153, 225, 0.4);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 1000;
  text-decoration: none;
  border: none;
  cursor: pointer;
  animation: fab-pulse 2s infinite;
  /* Ensure it's not affected by other styles */
  margin: 0;
  padding: 0;
  outline: none;
}

.add-story-fab i {
  font-size: 24px;
}

.fab-tooltip {
  position: absolute;
  right: 70px;
  background-color: rgba(44, 82, 130, 0.9); /* Darker blue with transparency */
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.fab-tooltip::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -5px;
  transform: translateY(-50%);
  border-width: 5px 0 5px 5px;
  border-style: solid;
  border-color: transparent transparent transparent rgba(44, 82, 130, 0.9);
}

.add-story-fab:hover .fab-tooltip,
.add-story-fab:focus .fab-tooltip {
  opacity: 1;
  visibility: visible;
}

.add-story-fab:hover,
.add-story-fab:focus {
  background-color: #3182ce; /* Darker blue on hover */
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 6px 15px rgba(66, 153, 225, 0.5);
  animation: none;
}

.add-story-fab:active {
  transform: translateY(0) scale(0.95);
}

@keyframes fab-pulse {
  0% { box-shadow: 0 0 0 0 rgba(66, 153, 225, 0.7); }
  70% { box-shadow: 0 0 0 15px rgba(66, 153, 225, 0); }
  100% { box-shadow: 0 0 0 0 rgba(66, 153, 225, 0); }
}
