import L from "leaflet";
import "leaflet-providers";
import "leaflet/dist/leaflet.css";
import { showFormattedDate } from "../../utils/index";
import MapsPresenter from "./maps-presenter";

export default class MapsPage {
  constructor() {
    this.stories = [];
    this.isLoading = false;
    this.map = null;
    this.markers = [];
    this._presenter = new MapsPresenter({
      view: this,
    });
  }

  async render() {
    return `
      <section class="container">
        <h2 class="maps-title">🗺️ Story Map</h2>

        <div id="loading-map" class="loading-indicator" style="display: flex; justify-content: center; margin: 2rem 0;">
          <div class="spinner"></div>
          <p>Loading stories...</p>
        </div>

        <div id="map-container" class="map-container-fullscreen">
          <!-- Map will be loaded here -->
        </div>

        <div id="error-container" style="display: none; text-align: center; color: red; margin: 2rem 0;">
          <p>Failed to load stories. Please try again later.</p>
        </div>
      </section>
    `;
  }

  async afterRender() {
    // Initialize the presenter
    await this._presenter.init();
  }

  // View interface methods for the presenter
  showLoading() {
    this.isLoading = true;
    document.getElementById("loading-map").style.display = "flex";
    document.getElementById("error-container").style.display = "none";
  }

  hideLoading() {
    this.isLoading = false;
    document.getElementById("loading-map").style.display = "none";
  }

  showError(message) {
    this.isLoading = false;
    document.getElementById("loading-map").style.display = "none";
    const errorContainer = document.getElementById("error-container");
    if (message) {
      errorContainer.querySelector("p").textContent = message;
    }
    errorContainer.style.display = "block";
  }

  addMarkers(stories) {
    this.stories = stories.filter((story) => story.lat && story.lon);

    if (this.stories.length === 0) {
      const errorContainer = document.getElementById("error-container");
      errorContainer.innerHTML =
        "<p>No stories with location found. Add a story with location first!</p>";
      errorContainer.style.display = "block";
    } else {
      this.addMarkersToMap();
    }
  }

  initMap() {
    // Fix Leaflet's default icon path issues
    this.fixLeafletDefaultIconPath();

    // Create map instance with Bintaro Sektor 9 as default location
    this.map = L.map("map-container").setView([-6.2730848, 106.7125006], 13); // Bintaro Sektor 9 centered

    // Define base layers
    const baseLayers = {
      OpenStreetMap: L.tileLayer(
        "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
        {
          attribution:
            '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        }
      ),
      "Esri WorldImagery": L.tileLayer(
        "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",
        {
          attribution:
            "Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community",
        }
      ),
      "Carto Dark": L.tileLayer(
        "https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png",
        {
          attribution:
            '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
          subdomains: 'abcd',
          maxZoom: 19
        }
      ),
      "Carto Voyager": L.tileLayer(
        "https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png",
        {
          attribution:
            '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
          subdomains: 'abcd',
          maxZoom: 19
        }
      ),
      "Stamen Terrain": L.tileLayer(
        "https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}{r}.png",
        {
          attribution:
            'Map tiles by <a href="http://stamen.com">Stamen Design</a>, <a href="http://creativecommons.org/licenses/by/3.0">CC BY 3.0</a> &mdash; Map data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          subdomains: 'abcd',
          minZoom: 0,
          maxZoom: 18
        }
      ),
    };

    // Add the default layer to the map
    baseLayers["OpenStreetMap"].addTo(this.map);

    // Create layer control and add it to the map
    L.control
      .layers(baseLayers, null, {
        position: "topright",
        collapsed: false,
      })
      .addTo(this.map);

    // Add instructions for the user
    const instructionDiv = document.createElement("div");
    instructionDiv.className = "map-instruction";
    instructionDiv.innerHTML =
      "<p>📍 Click on the map to see location coordinates</p>";
    document.getElementById("map-container").appendChild(instructionDiv);

    // Add a draggable marker
    this.userMarker = null;

    // Add click event listener to the map
    this.map.on("click", (e) => {
      const { lat, lng } = e.latlng;

      // Display selected coordinates
      const locationInfo = document.createElement("div");
      locationInfo.className = "location-selected";
      locationInfo.innerHTML = `<p>Selected location: ${lat.toFixed(
        6
      )}, ${lng.toFixed(6)}</p>`;

      // Remove previous location info if exists
      const oldInfo = document.querySelector(".location-selected");
      if (oldInfo) {
        oldInfo.remove();
      }

      document.getElementById("map-container").appendChild(locationInfo);

      // Update marker
      if (this.userMarker) {
        this.userMarker.setLatLng(e.latlng);
      } else {
        // Create marker with larger and colored icon
        const customIcon = L.icon({
          iconUrl:
            "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png",
          iconSize: [30, 45],
          iconAnchor: [15, 45],
          popupAnchor: [0, -45],
        });

        this.userMarker = L.marker(e.latlng, {
          icon: customIcon,
          draggable: true,
        }).addTo(this.map);

        // Add popup to marker
        this.userMarker
          .bindPopup(
            `
          <div class="marker-popup">
            <h4>Your Selected Location</h4>
            <p>Coordinates: ${lat.toFixed(6)}, ${lng.toFixed(6)}</p>
            <p>You can drag this marker to select a precise location.</p>
          </div>
        `
          )
          .openPopup();

        // Add event listener for marker drag end
        this.userMarker.on("dragend", (event) => {
          const marker = event.target;
          const position = marker.getLatLng();

          // Update popup
          marker.setPopupContent(`
            <div class="marker-popup">
              <h4>Your Selected Location</h4>
              <p>Coordinates: ${position.lat.toFixed(
                6
              )}, ${position.lng.toFixed(6)}</p>
              <p>You can drag this marker to select a precise location.</p>
            </div>
          `);

          // Update location info
          const locationInfo = document.querySelector(".location-selected");
          if (locationInfo) {
            locationInfo.innerHTML = `<p>Selected location: ${position.lat.toFixed(
              6
            )}, ${position.lng.toFixed(6)}</p>`;
          }
        });
      }
    });
  }

  fixLeafletDefaultIconPath() {
    // Get Leaflet's default icon
    const defaultIcon = L.Icon.Default.prototype.options;

    // Set the path to the icon images using absolute URLs
    defaultIcon.iconUrl =
      "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png";
    defaultIcon.iconRetinaUrl =
      "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png";
    defaultIcon.shadowUrl =
      "https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png";
  }

  addMarkersToMap() {
    // Clear existing story markers
    this.markers.forEach((marker) => this.map.removeLayer(marker));
    this.markers = [];

    if (this.stories.length === 0) return;

    // Create bounds to fit all markers
    const bounds = L.latLngBounds();

    // Add markers for each story with location
    this.stories.forEach((story) => {
      const marker = L.marker([story.lat, story.lon]).addTo(this.map)
        .bindPopup(`
          <div class="marker-popup">
            <h4>${story.name}</h4>
            <img src="${story.photoUrl}" alt="${
        story.description
      }" style="width: 100%; max-height: 150px; object-fit: cover;">
            <p>${story.description}</p>
            <small>${showFormattedDate(story.createdAt)}</small>
            <div style="margin-top: 10px; text-align: center;">
              <a href="#/stories/${
                story.id
              }" style="display: inline-block; padding: 5px 10px; background-color: #ffe4e6; color: #d6336c; text-decoration: none; border-radius: 4px; font-weight: bold;">View Detail</a>
            </div>
          </div>
        `);

      this.markers.push(marker);
      bounds.extend([story.lat, story.lon]);
    });

    // If user marker exists, include it in the bounds
    if (this.userMarker) {
      const userPos = this.userMarker.getLatLng();
      bounds.extend([userPos.lat, userPos.lng]);
    }

    // Fit map to bounds with padding
    this.map.fitBounds(bounds, { padding: [50, 50] });
  }
}
