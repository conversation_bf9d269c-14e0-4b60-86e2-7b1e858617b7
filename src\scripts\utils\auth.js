const AUTH_KEY = "story_app_auth";

const AuthService = {
  /**
   * Set user authentication data in localStorage
   * @param {Object} userData - User data including token
   */
  setAuth(userData) {
    localStorage.setItem(AUTH_KEY, JSON.stringify(userData));
  },

  /**
   * Get user authentication data from localStorage
   * @returns {Object|null} User data or null if not authenticated
   */
  getAuth() {
    const authData = localStorage.getItem(AUTH_KEY);
    if (!authData) {
      return null;
    }

    try {
      return JSON.parse(authData);
    } catch (error) {
      console.error("Error parsing auth data:", error);
      return null;
    }
  },

  /**
   * Check if user is authenticated
   * @returns {boolean} True if authenticated, false otherwise
   */
  isAuthenticated() {
    const auth = this.getAuth();
    console.log("Auth data:", auth);
    return !!auth;
  },

  /**
   * Remove authentication data (logout)
   */
  clearAuth() {
    localStorage.removeItem(AUTH_KEY);
  },
};

export default AuthService;
