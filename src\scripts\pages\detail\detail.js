import L from "leaflet";
import "leaflet/dist/leaflet.css";
import "../../components/favorite-button";
import { showFormattedDate } from "../../utils/index";
import DetailPresenter from "./detail-presenter";

export default class DetailPage {
  constructor() {
    this.story = null;
    this.isLoading = false;
    this.map = null;
    this.marker = null;
    this._presenter = new DetailPresenter({
      view: this,
    });
  }

  async render() {
    return `
      <section class="container">
        <h2 class="detail-title">📖 Story Detail</h2>

        <div id="loading-detail" class="loading-indicator" style="display: flex; justify-content: center; margin: 2rem 0;">
          <div class="spinner"></div>
          <p>Loading story...</p>
        </div>

        <div id="detail-container" class="detail-container" style="display: none;">
          <!-- Story detail will be loaded here -->
        </div>

        <div id="map-container" class="map-container" style="display: none;">
          <!-- Map will be loaded here if story has location -->
        </div>

        <div id="error-container" style="display: none; text-align: center; margin: 2rem 0; padding: 20px; background-color: #fff3f3; border-radius: 8px; border: 1px solid #ffcccc;">
          <p style="color: #e53e3e; font-weight: 500; margin-bottom: 15px;">Failed to load story. Please try again later.</p>
          <p style="color: #718096; font-size: 0.9rem;">This might happen if the story doesn't exist or there was a problem with the database.</p>
        </div>

        <div class="back-button-container" style="margin-top: 2rem; text-align: center;">
          <a href="#/home" class="back-button">Back to Home</a>
        </div>
      </section>
    `;
  }

  async afterRender() {
    try {
      // Initialize the presenter
      await this._presenter.init();
    } catch (error) {
      console.error("Error in detail page afterRender:", error);
      this.showError("Failed to load story details. Please try again later.");
    }
  }

  // View interface methods for the presenter
  showLoading() {
    this.isLoading = true;
    document.getElementById("loading-detail").style.display = "flex";
    document.getElementById("detail-container").style.display = "none";
    document.getElementById("map-container").style.display = "none";
    document.getElementById("error-container").style.display = "none";
  }

  hideLoading() {
    this.isLoading = false;
    document.getElementById("loading-detail").style.display = "none";
  }

  showStoryDetail(story) {
    this.story = story;
    const detailContainer = document.getElementById("detail-container");

    // Pre-cache the story image for offline use
    this.preCacheStoryImage(this.story);

    // Render story detail
    detailContainer.innerHTML = `
      <article class="detail-card">
        <figure>
          <img src="${this.story.photoUrl}"
               alt="Story image by ${
                 this.story.name
               }: ${this.story.description.substring(0, 100)}${
      this.story.description.length > 100 ? "..." : ""
    }"
               class="detail-img"
               loading="eager"
               data-fallback="/images/placeholder.svg"
               data-id="${this.story.id}"
               onerror="this.onerror=null; this.src=this.getAttribute('data-fallback'); console.log('Image failed to load, using fallback: ' + this.getAttribute('data-fallback'));" />
          <figcaption class="visually-hidden">Photo for story by ${
            this.story.name
          }</figcaption>
        </figure>
        <div class="detail-content">
          <h2 class="detail-name">${this.story.name}</h2>
          <p class="detail-description">${this.story.description}</p>
          <time class="detail-date" datetime="${new Date(
            this.story.createdAt
          ).toISOString()}">${showFormattedDate(this.story.createdAt)}</time>
          ${
            this.story.lat && this.story.lon
              ? `
            <div class="detail-location" aria-label="Story location coordinates">
              <span class="location-dot" aria-hidden="true">●</span> Location: ${this.story.lat.toFixed(
                4
              )}, ${this.story.lon.toFixed(4)}
            </div>
          `
              : ""
          }

          <!-- Favorite Button -->
          <favorite-button id="favorite-button" story-id="${
            this.story.id
          }"></favorite-button>
        </div>
      </article>
    `;

    // Show detail container
    detailContainer.style.display = "block";

    // Set story data to favorite button
    const favoriteButton = document.getElementById("favorite-button");
    if (favoriteButton) {
      favoriteButton.story = this.story;
    }

    // Add event listener to handle image loading errors with better fallbacks
    setTimeout(() => {
      const detailImg = document.querySelector(".detail-img");
      if (detailImg) {
        // Force reload the image to ensure it's loaded from cache if available
        const originalSrc = detailImg.src;
        detailImg.src = "";
        detailImg.src = originalSrc;

        // Add error handler
        detailImg.addEventListener("error", function () {
          console.log(`Detail image error detected for: ${this.src}`);
          const fallbackSrc =
            this.getAttribute("data-fallback") || "/images/placeholder.svg";

          // Try to find the image in cache
          if ("caches" in window) {
            caches
              .match(originalSrc)
              .then((response) => {
                if (response) {
                  // If found in cache, use it
                  console.log(`Found detail image in cache: ${originalSrc}`);
                  return response.blob();
                } else {
                  // If not found, try to find a picsum image
                  return caches
                    .open("story-app-picsum-images")
                    .then((cache) => cache.keys())
                    .then((keys) => {
                      if (keys.length > 0) {
                        const randomIndex = Math.floor(
                          Math.random() * keys.length
                        );
                        return caches.match(keys[randomIndex].url);
                      }
                      return null;
                    })
                    .then((response) => (response ? response.blob() : null));
                }
              })
              .then((blob) => {
                if (blob) {
                  const objectURL = URL.createObjectURL(blob);
                  this.src = objectURL;
                } else {
                  // Use the fallback image
                  this.src = fallbackSrc;
                }
              })
              .catch((error) => {
                console.error(
                  "Error retrieving detail image from cache:",
                  error
                );
                this.src = fallbackSrc;
              });
          } else {
            // If caches API not available, use fallback
            this.src = fallbackSrc;
          }
        });
      }
    }, 500); // Small delay to ensure DOM is fully processed
  }

  // Method to pre-cache story image
  preCacheStoryImage(story) {
    if (!("caches" in window) || !story || !story.photoUrl) {
      return;
    }

    try {
      console.log("Pre-caching detail story image...");

      // Open the cache
      caches.open("story-app-detail-images").then((cache) => {
        // Check if already in cache
        cache.match(story.photoUrl).then((match) => {
          if (match) {
            console.log(`Detail image already cached: ${story.photoUrl}`);
            return;
          }

          // Create an image element to preload the image
          const img = new Image();
          img.onload = () => {
            console.log(`Detail image loaded successfully: ${story.photoUrl}`);

            // Force the browser to cache the image
            fetch(story.photoUrl, { mode: "no-cors" })
              .then((response) => {
                if (response) {
                  cache.put(story.photoUrl, response);
                  console.log(
                    `Detail image cached successfully: ${story.photoUrl}`
                  );
                }
              })
              .catch((error) =>
                console.error(`Failed to cache detail image: ${error}`)
              );
          };

          img.onerror = () => {
            console.error(`Failed to load detail image: ${story.photoUrl}`);

            // If loading fails, try to use a picsum image as fallback
            caches.open("story-app-picsum-images").then((picsumCache) => {
              picsumCache.keys().then((keys) => {
                if (keys.length > 0) {
                  const randomIndex = Math.floor(Math.random() * keys.length);
                  picsumCache.match(keys[randomIndex].url).then((response) => {
                    if (response) {
                      cache.put(story.photoUrl, response);
                      console.log(
                        `Using picsum image as fallback for detail: ${keys[randomIndex].url}`
                      );
                    }
                  });
                }
              });
            });
          };

          // Start loading the image
          img.src = story.photoUrl;
        });
      });
    } catch (error) {
      console.error("Error pre-caching detail image:", error);
    }
  }

  showMap(story) {
    document.getElementById("map-container").style.display = "block";
    this.initMap();
  }

  showError(message) {
    this.isLoading = false;
    document.getElementById("loading-detail").style.display = "none";
    const errorContainer = document.getElementById("error-container");
    if (message) {
      errorContainer.querySelector("p").textContent = message;
    }
    errorContainer.style.display = "block";
  }

  initMap() {
    // Fix Leaflet's default icon path issues
    this.fixLeafletDefaultIconPath();

    // Create map instance
    this.map = L.map("map-container").setView(
      [this.story.lat, this.story.lon],
      13
    );

    // Add tile layer (OpenStreetMap)
    L.tileLayer(
      "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
      {
        attribution:
          '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
      }
    ).addTo(this.map);

    // Add marker for story location
    this.marker = L.marker([this.story.lat, this.story.lon])
      .addTo(this.map)
      .bindPopup(
        `
        <div class="marker-popup">
          <h4>${this.story.name}</h4>
          <p>${this.story.description}</p>
        </div>
      `
      )
      .openPopup();
  }

  fixLeafletDefaultIconPath() {
    // Get Leaflet's default icon
    const defaultIcon = L.Icon.Default.prototype.options;

    // Set the path to the icon images using absolute URLs
    defaultIcon.iconUrl =
      "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png";
    defaultIcon.iconRetinaUrl =
      "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png";
    defaultIcon.shadowUrl =
      "https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png";
  }
}
