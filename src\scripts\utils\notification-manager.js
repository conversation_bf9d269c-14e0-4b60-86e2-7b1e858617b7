import Story<PERSON><PERSON> from "../data/api";
import AuthService from "./auth";
import NotificationHelper from "./notification-helper";

class NotificationManager {
  constructor({ storyAPI, authService, notificationHelper } = {}) {
    this._storyAPI = storyAPI || StoryAPI;
    this._authService = authService || AuthService;
    this._notificationHelper = notificationHelper || NotificationHelper;
    this._serviceWorkerRegistration = null;
  }

  // Initialize notification manager
  async init() {
    try {
      if (!this._notificationHelper.isPushNotificationSupported()) {
        console.log("Push notification is not supported in this browser");
        return false;
      }

      this._serviceWorkerRegistration = await this._notificationHelper.registerServiceWorker();
      if (!this._serviceWorkerRegistration) {
        console.log("Failed to register service worker");
        return false;
      }

      return true;
    } catch (error) {
      console.error("Error initializing notification manager:", error);
      return false;
    }
  }

  // Check if notification is supported
  isNotificationSupported() {
    return this._notificationHelper.isPushNotificationSupported();
  }

  // Check if already subscribed to push notification
  async isSubscribed() {
    const button = document.getElementById("notification-toggle-nav");
    if (button && button.classList.contains("active")) {
      return true;
    }

    if (!this._serviceWorkerRegistration) {
      return false;
    }

    try {
      return await this._notificationHelper.isSubscribedToPushNotification(
        this._serviceWorkerRegistration
      );
    } catch (error) {
      console.error("Error checking subscription status:", error);
      return false;
    }
  }

  // Subscribe to push notification
  async subscribe() {
    try {
      console.log("Starting notification subscription process...");

      // Check if user is authenticated
      const auth = this._authService.getAuth();
      if (!auth || !auth.token) {
        console.log("User is not authenticated");
        return false;
      }

      // Check if already subscribed
      const isAlreadySubscribed = await this.isSubscribed();
      if (isAlreadySubscribed) {
        console.log("Already subscribed to notifications");
        return true;
      }

      // Check if service worker is registered
      if (!this._serviceWorkerRegistration) {
        console.log("Service worker not registered, attempting to register...");
        this._serviceWorkerRegistration = await this._notificationHelper.registerServiceWorker();
        if (!this._serviceWorkerRegistration) {
          console.log("Failed to register service worker");
          return false;
        }
      }

      // Request notification permission
      console.log("Requesting notification permission...");
      const permissionGranted = await this._notificationHelper.requestPermission();
      if (!permissionGranted) {
        console.log("Notification permission denied");
        return false;
      }

      // Subscribe to push notification
      console.log("Subscribing to push notification...");
      const subscription = await this._notificationHelper.subscribeToPushNotification(
        this._serviceWorkerRegistration
      );

      if (!subscription) {
        console.log("Failed to subscribe to push notification");
        return false;
      }

      // Log subscription for debugging
      console.log("Subscription object:", subscription);

      // Register subscription to server
      console.log("Registering subscription to server...");
      try {
        const response = await this._storyAPI.subscribeNotification(
          subscription,
          auth.token
        );

        if (response.error) {
          console.log("Failed to register subscription to server:", response.message);
          return false;
        }

        console.log("Subscription successfully registered with server!");
        return true;
      } catch (apiError) {
        console.error("API error during subscription:", apiError);
        return true; // Consider local subscription a success
      }
    } catch (error) {
      console.error("Error subscribing to push notification:", error);
      return false;
    }
  }

  // Unsubscribe from push notification
  async unsubscribe() {
    try {
      console.log("Starting notification unsubscription process...");

      // Check if user is authenticated
      const auth = this._authService.getAuth();
      if (!auth || !auth.token) {
        console.log("User is not authenticated");
        return true; // Simulate success if not authenticated
      }

      // Check if service worker registration exists
      if (!this._serviceWorkerRegistration) {
        console.log("No service worker registration found");
        return true;
      }

      // Get current subscription
      console.log("Getting current subscription...");
      const subscription = await this._serviceWorkerRegistration.pushManager.getSubscription();
      if (!subscription) {
        console.log("No subscription to unsubscribe from");
        return true;
      }

      // Convert subscription to JSON
      const subscriptionJson = subscription.toJSON();
      console.log("Unsubscribing from subscription:", subscriptionJson);

      // Unsubscribe locally
      console.log("Unsubscribing locally...");
      const localResult = await this._notificationHelper.unsubscribeFromPushNotification(
        this._serviceWorkerRegistration
      );

      if (!localResult) {
        console.log("Failed to unsubscribe locally, but continuing...");
      }

      // Unregister subscription from server
      console.log("Unregistering subscription from server...");
      try {
        const response = await this._storyAPI.unsubscribeNotification(
          subscriptionJson,
          auth.token
        );

        if (response.error) {
          console.log("Failed to unregister subscription from server:", response.message);
          return false;
        }

        console.log("Successfully unsubscribed from push notification");
        return true;
      } catch (apiError) {
        console.error("API error during unsubscription:", apiError);
        return true; // Consider local unsubscription a success
      }
    } catch (error) {
      console.error("Error unsubscribing from push notification:", error);
      return true; // Simulate success to avoid breaking the app
    }
  }
}

export default NotificationManager;