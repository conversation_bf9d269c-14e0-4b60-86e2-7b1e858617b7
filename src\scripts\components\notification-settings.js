class NotificationSettings extends HTMLElement {
  constructor() {
    super();
    this._isNotificationSupported = false;
    this._isSubscribed = false;
  }

  async connectedCallback() {
    // Render immediately with default state
    this.render();

    // Then try to initialize notification settings
    try {
      // Wait a bit to ensure notification<PERSON>anager is initialized
      setTimeout(async () => {
        try {
          this._notificationManager = window.notificationManager;

          if (this._notificationManager) {
            this._isNotificationSupported =
              this._notificationManager.isNotificationSupported();

            if (this._isNotificationSupported) {
              try {
                this._isSubscribed =
                  await this._notificationManager.isSubscribed();
              } catch (error) {
                console.error("Error checking subscription status:", error);
                this._isSubscribed = false;
              }
            }
          }

          // Re-render with updated state
          this.render();
          this._initEventListeners();
        } catch (error) {
          console.error("Error initializing notification settings:", error);
          // Keep the default rendering
        }
      }, 2000); // Wait 2 seconds to ensure notification manager is initialized
    } catch (error) {
      console.error("Error in notification settings connectedCallback:", error);
      // Keep the default rendering
    }
  }

  render() {
    // Don't render anything - we'll use the navigation menu button instead
    this.innerHTML = ``;
  }

  _initEventListeners() {
    const toggleButton = this.querySelector("#notification-toggle");
    if (toggleButton) {
      toggleButton.addEventListener("click", async () => {
        if (this._isSubscribed) {
          await this._unsubscribe();
        } else {
          await this._subscribe();
        }
      });
    }
  }

  async _subscribe() {
    try {
      const success = await this._notificationManager.subscribe();
      if (success) {
        this._isSubscribed = true;
        this.render();
        this._initEventListeners();
        alert("Successfully subscribed to notifications!");
      } else {
        alert("Failed to subscribe to notifications. Please try again.");
      }
    } catch (error) {
      console.error("Error subscribing to notifications:", error);
      alert("An error occurred while subscribing to notifications.");
    }
  }

  async _unsubscribe() {
    try {
      const success = await this._notificationManager.unsubscribe();
      if (success) {
        this._isSubscribed = false;
        this.render();
        this._initEventListeners();
        alert("Successfully unsubscribed from notifications!");
      } else {
        alert("Failed to unsubscribe from notifications. Please try again.");
      }
    } catch (error) {
      console.error("Error unsubscribing from notifications:", error);
      alert("An error occurred while unsubscribing from notifications.");
    }
  }
}

customElements.define("notification-settings", NotificationSettings);

export default NotificationSettings;
