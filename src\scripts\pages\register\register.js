import RegisterPresenter from "./register-presenter";

export default class Register {
  constructor() {
    this._presenter = new RegisterPresenter({
      view: this,
    });
  }
  async render() {
    return `
        <div class="register-container">
          <h2>📝 Register</h2>
          <form id="registerForm">
            <div class="form-group">
              <label for="name">👤 Name</label>
              <input type="text" id="name" name="name" placeholder="Enter your name" required />
            </div>
            <div class="form-group">
              <label for="email">📧 Email</label>
              <input type="email" id="email" name="email" placeholder="Enter your email" required />
            </div>
            <div class="form-group">
              <label for="password">🔒 Password</label>
              <input type="password" id="password" name="password" placeholder="Enter your password" required minlength="8" />
              <small>Password must be at least 8 characters</small>
            </div>
            <div class="form-group">
              <label for="confirmPassword">🔐 Confirm Password</label>
              <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirm your password" required />
            </div>
            <button type="submit" id="register-button">📝 Register</button>
            <div id="loading-indicator" class="loading-indicator" style="display: none;">
              <div class="spinner"></div>
              <p>Registering...</p>
            </div>
          </form>
          <p>Already have an account? <a href="#/login">Login here</a></p>
        </div>
      `;
  }

  async afterRender() {
    const form = document.getElementById("registerForm");

    form.addEventListener("submit", async (event) => {
      event.preventDefault();

      const name = document.getElementById("name").value;
      const email = document.getElementById("email").value;
      const password = document.getElementById("password").value;
      const confirmPassword = document.getElementById("confirmPassword").value;

      // Basic validation
      if (!name || !email || !password || !confirmPassword) {
        this.showErrorMessage("Please fill in all fields");
        return;
      }

      if (password !== confirmPassword) {
        this.showErrorMessage("Passwords do not match");
        return;
      }

      if (password.length < 8) {
        this.showErrorMessage("Password must be at least 8 characters");
        return;
      }

      // Use presenter to handle registration
      const success = await this._presenter.register(name, email, password);

      if (success) {
        // Redirect to login page
        window.location.hash = "#/login";
      }
    });
  }

  // View interface methods for the presenter
  showLoading() {
    document.getElementById("register-button").style.display = "none";
    document.getElementById("loading-indicator").style.display = "flex";
  }

  hideLoading() {
    document.getElementById("register-button").style.display = "block";
    document.getElementById("loading-indicator").style.display = "none";
  }

  showSuccessMessage(message) {
    alert(message);
  }

  showErrorMessage(message) {
    alert(message);
  }
}
