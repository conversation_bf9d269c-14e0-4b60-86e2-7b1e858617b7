import StoryIdb from '../data/story-idb';

class FavoriteButton extends HTMLElement {
  constructor() {
    super();
    this._story = null;
    this._isFavorite = false;
  }

  set story(story) {
    this._story = story;
    this._render();
  }

  async connectedCallback() {
    if (this.hasAttribute('story-id')) {
      const storyId = this.getAttribute('story-id');
      this._isFavorite = await StoryIdb.hasStory(storyId);
      this._render();
    }
  }

  async _render() {
    this.innerHTML = `
      <button class="favorite-button ${this._isFavorite ? 'favorite' : ''}" aria-label="${this._isFavorite ? 'Remove from favorites' : 'Add to favorites'}">
        ${this._isFavorite ? '❤️ Remove from Favorites' : '🤍 Add to Favorites'}
      </button>
    `;

    this.querySelector('.favorite-button').addEventListener('click', async (event) => {
      event.preventDefault();
      await this._toggleFavorite();
    });
  }

  async _toggleFavorite() {
    try {
      if (!this._story) {
        console.error('No story data available');
        return;
      }

      if (this._isFavorite) {
        // Remove from favorites
        await StoryIdb.deleteStory(this._story.id);
        this._isFavorite = false;
        alert('Story removed from favorites');
      } else {
        // Add to favorites
        await StoryIdb.saveStory(this._story);
        this._isFavorite = true;
        alert('Story added to favorites');
      }

      this._render();
    } catch (error) {
      console.error('Error toggling favorite:', error);
      alert('Failed to update favorites. Please try again.');
    }
  }
}

customElements.define('favorite-button', FavoriteButton);

export default FavoriteButton;
