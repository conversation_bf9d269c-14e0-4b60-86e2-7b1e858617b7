import StoryAPI from "../../data/api";
import AuthService from "../../utils/auth";

class MapsPresenter {
  constructor({ view, authService, storyAPI }) {
    this._view = view;
    this._authService = authService || AuthService;
    this._storyAPI = storyAPI || StoryAPI;
    this._stories = [];
  }

  async init() {
    await this.loadStoriesWithLocation();
  }

  async loadStoriesWithLocation() {
    try {
      this._view.showLoading();

      // Get auth data
      const auth = this._authService.getAuth();

      if (!auth || !auth.token) {
        throw new Error("Authentication token not found");
      }

      // Fetch stories from API with location data
      const response = await this._storyAPI.getStories(auth.token, {
        location: 1, // Always get stories with location
      });

      this._view.hideLoading();

      if (response.error === false) {
        // Store stories
        this._stories = response.listStory || [];

        // Initialize map and add markers
        this._view.initMap();
        this._view.addMarkers(this._stories);
      } else {
        throw new Error(response.message || "Failed to load stories");
      }
    } catch (error) {
      console.error("Error loading stories for map:", error);
      this._view.showError(error.message);
    }
  }

  getStories() {
    return this._stories;
  }
  _initMap() {
    // Inisialisasi peta Leaflet
    this.map = L.map(this.mapElement).setView([-6.2088, 106.8456], 13); // Default: Jakarta

    // Tambahkan tile layer dari OpenStreetMap
    L.tileLayer(
      "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
      {
        attribution:
          '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19,
      }
    ).addTo(this.map);

    // Tambahkan layer control dengan beberapa opsi layer
    const baseMaps = {
      OpenStreetMap: L.tileLayer(
        "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
        {
          attribution:
            '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          maxZoom: 19,
        }
      ),
      "Esri WorldImagery": L.tileLayer(
        "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",
        {
          attribution:
            "Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community",
          maxZoom: 19,
        }
      ),
    };

    L.control.layers(baseMaps).addTo(this.map);

    // Tambahkan event listener untuk klik pada peta
    this.map.on("click", (e) => {
      const { lat, lng } = e.latlng;
      this._updateMarker(lat, lng);
    });

    // Resize peta setelah dirender
    setTimeout(() => {
      this.map.invalidateSize();
    }, 100);
  }
}

export default MapsPresenter;
